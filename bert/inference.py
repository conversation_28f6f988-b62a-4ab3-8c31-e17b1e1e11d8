"""
BERT模型推理和测试
"""

import torch
import torch.nn.functional as F
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import argparse
from transformers import AutoTokenizer

from config import BertConfig, LoggingConfig
from model import BertForMaskedLM
from utils import setup_logging

logger = logging.getLogger('BERT')


class BertInference:
    """BERT推理类"""

    def __init__(self, model_path: str, tokenizer_name: str = "bert-base-uncased"):
        """
        初始化推理器

        Args:
            model_path: 模型路径
            tokenizer_name: tokenizer名称
        """
        self.model_path = Path(model_path)
        self.device = self._get_device()

        # 加载配置
        self.config = self._load_config()

        # 加载模型
        self.model = self._load_model()

        # 加载tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        if self.tokenizer.mask_token is None:
            self.tokenizer.add_special_tokens({'mask_token': '[MASK]'})

        logger.info(f"推理器初始化完成")
        logger.info(f"模型路径: {model_path}")
        logger.info(f"设备: {self.device}")

    def _get_device(self) -> torch.device:
        """获取设备"""
        if torch.backends.mps.is_available():
            return torch.device("mps")
        elif torch.cuda.is_available():
            return torch.device("cuda")
        else:
            return torch.device("cpu")

    def _load_config(self) -> BertConfig:
        """加载模型配置"""
        config_path = self.model_path / "config.json"
        if not config_path.exists():
            logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
            return BertConfig()

        with open(config_path, 'r') as f:
            config_dict = json.load(f)

        return BertConfig(**config_dict)

    def _load_model(self) -> BertForMaskedLM:
        """加载模型"""
        model = BertForMaskedLM(self.config)

        # 加载权重
        model_file = self.model_path / "pytorch_model.bin"
        if model_file.exists():
            state_dict = torch.load(model_file, map_location=self.device)
            model.load_state_dict(state_dict)
            logger.info(f"成功加载模型权重: {model_file}")
        else:
            logger.warning(f"模型权重文件不存在: {model_file}，使用随机初始化权重")

        model.to(self.device)
        model.eval()

        return model

    def predict_masked_tokens(
        self,
        text: str,
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        预测掩码token

        Args:
            text: 包含[MASK]的文本
            top_k: 返回top-k预测结果

        Returns:
            预测结果列表
        """
        # tokenize输入
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=self.config.max_position_embeddings
        )

        # 移动到设备
        inputs = {k: v.to(self.device) for k, v in inputs.items()}

        # 找到MASK token的位置
        mask_token_id = self.tokenizer.mask_token_id
        mask_positions = (inputs["input_ids"] == mask_token_id).nonzero(as_tuple=True)

        if len(mask_positions[0]) == 0:
            logger.warning("输入文本中没有找到[MASK] token")
            return []

        # 前向传播
        with torch.no_grad():
            outputs = self.model(**inputs)
            predictions = outputs["logits"]

        results = []
        for i, pos in enumerate(mask_positions[1]):
            # 获取该位置的预测概率
            mask_predictions = predictions[0, pos]
            probs = F.softmax(mask_predictions, dim=-1)

            # 获取top-k结果
            top_k_probs, top_k_indices = torch.topk(probs, top_k)

            # 转换为token
            top_k_tokens = [self.tokenizer.decode([idx]) for idx in top_k_indices]

            results.append({
                "position": pos.item(),
                "predictions": [
                    {
                        "token": token.strip(),
                        "probability": prob.item()
                    }
                    for token, prob in zip(top_k_tokens, top_k_probs)
                ]
            })

        return results

    def fill_mask(self, text: str) -> str:
        """
        填充掩码（返回最可能的token）

        Args:
            text: 包含[MASK]的文本

        Returns:
            填充后的文本
        """
        predictions = self.predict_masked_tokens(text, top_k=1)

        filled_text = text
        for pred in predictions:
            if pred["predictions"]:
                best_token = pred["predictions"][0]["token"]
                filled_text = filled_text.replace("[MASK]", best_token, 1)

        return filled_text

    def get_embeddings(self, text: str) -> torch.Tensor:
        """
        获取文本的BERT嵌入

        Args:
            text: 输入文本

        Returns:
            嵌入向量
        """
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=self.config.max_position_embeddings
        )

        inputs = {k: v.to(self.device) for k, v in inputs.items()}

        with torch.no_grad():
            outputs = self.model.bert(**inputs)
            # 使用[CLS] token的嵌入作为句子表示
            embeddings = outputs["pooler_output"]

        return embeddings

    def compute_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度

        Args:
            text1: 文本1
            text2: 文本2

        Returns:
            余弦相似度
        """
        emb1 = self.get_embeddings(text1)
        emb2 = self.get_embeddings(text2)

        # 计算余弦相似度
        similarity = F.cosine_similarity(emb1, emb2, dim=1)

        return similarity.item()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BERT模型推理")
    parser.add_argument("--model_path", type=str, required=True,
                       help="模型路径")
    parser.add_argument("--tokenizer_name", type=str, default="bert-base-uncased",
                       help="tokenizer名称")
    parser.add_argument("--text", type=str, default=None,
                       help="要处理的文本")
    parser.add_argument("--mode", type=str, default="mask",
                       choices=["mask", "similarity", "embedding"],
                       help="推理模式")
    parser.add_argument("--text2", type=str, default=None,
                       help="第二个文本（用于相似度计算）")

    args = parser.parse_args()

    # 设置日志
    logging_config = LoggingConfig(log_level="INFO")
    setup_logging(logging_config)

    # 创建推理器
    inference = BertInference(args.model_path, args.tokenizer_name)

    if args.mode == "mask":
        if not args.text:
            # 使用示例文本
            test_texts = [
                "The capital of France is [MASK].",
                "I love to eat [MASK] for breakfast.",
                "The weather today is [MASK].",
                "She is a very [MASK] person.",
                "The [MASK] is shining brightly."
            ]
        else:
            test_texts = [args.text]

        logger.info("=== 掩码语言模型测试 ===")
        for text in test_texts:
            logger.info(f"输入: {text}")

            # 预测掩码
            predictions = inference.predict_masked_tokens(text, top_k=5)
            for pred in predictions:
                logger.info(f"位置 {pred['position']} 的预测:")
                for p in pred["predictions"]:
                    logger.info(f"  {p['token']}: {p['probability']:.4f}")

            # 填充掩码
            filled = inference.fill_mask(text)
            logger.info(f"填充结果: {filled}")
            logger.info("-" * 50)

    elif args.mode == "similarity":
        if not args.text or not args.text2:
            # 使用示例文本
            text_pairs = [
                ("I love cats.", "I adore felines."),
                ("The weather is nice.", "It's a beautiful day."),
                ("I'm going to school.", "I'm heading to the office."),
                ("She is happy.", "She is sad."),
                ("The book is interesting.", "The movie is boring.")
            ]
        else:
            text_pairs = [(args.text, args.text2)]

        logger.info("=== 文本相似度测试 ===")
        for text1, text2 in text_pairs:
            similarity = inference.compute_similarity(text1, text2)
            logger.info(f"文本1: {text1}")
            logger.info(f"文本2: {text2}")
            logger.info(f"相似度: {similarity:.4f}")
            logger.info("-" * 50)

    elif args.mode == "embedding":
        if not args.text:
            args.text = "This is a test sentence."

        logger.info("=== 文本嵌入测试 ===")
        logger.info(f"输入: {args.text}")

        embeddings = inference.get_embeddings(args.text)
        logger.info(f"嵌入维度: {embeddings.shape}")
        logger.info(f"嵌入向量前10维: {embeddings[0][:10].tolist()}")


if __name__ == "__main__":
    main()
