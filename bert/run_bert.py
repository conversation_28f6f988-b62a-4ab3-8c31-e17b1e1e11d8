#!/usr/bin/env python3
"""
BERT完整训练和微调脚本
支持预训练（MLM+NSP）和分类微调
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path


def run_pretraining(args):
    """运行预训练"""
    cmd = [
        sys.executable, "main.py",
        "--output_dir", args.output_dir,
        "--batch_size", str(args.batch_size),
        "--learning_rate", str(args.learning_rate),
        "--num_epochs", str(args.num_epochs),
        "--max_seq_length", str(args.max_seq_length),
        "--hidden_size", str(args.hidden_size),
        "--num_hidden_layers", str(args.num_hidden_layers),
        "--num_attention_heads", str(args.num_attention_heads),
        "--device", args.device,
        "--seed", str(args.seed),
        "--dataset_name", args.dataset_name,
        "--dataset_config", args.dataset_config
    ]
    
    if args.max_samples:
        cmd.extend(["--max_samples", str(args.max_samples)])
    
    if args.config_file:
        cmd.extend(["--config_file", args.config_file])
    
    print(f"运行预训练命令: {' '.join(cmd)}")
    subprocess.run(cmd)


def run_fine_tuning(args):
    """运行微调"""
    from fine_tuning import fine_tune_on_imdb
    from utils import setup_logging
    from config import LoggingConfig
    
    # 设置日志
    logging_config = LoggingConfig(log_level="INFO")
    setup_logging(logging_config)
    
    # 运行微调
    history = fine_tune_on_imdb(
        pretrained_model_path=args.pretrained_model_path,
        output_dir=args.output_dir,
        num_epochs=args.num_epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        max_samples=args.max_samples
    )
    
    print("微调完成！")
    if history["val_accuracy"]:
        print(f"最终验证准确率: {history['val_accuracy'][-1]:.4f}")


def run_inference(args):
    """运行推理"""
    cmd = [
        sys.executable, "inference.py",
        "--model_path", args.model_path,
        "--mode", args.mode
    ]
    
    if args.text:
        cmd.extend(["--text", args.text])
    
    if args.text2:
        cmd.extend(["--text2", args.text2])
    
    if args.tokenizer_name:
        cmd.extend(["--tokenizer_name", args.tokenizer_name])
    
    print(f"运行推理命令: {' '.join(cmd)}")
    subprocess.run(cmd)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BERT完整训练和微调脚本")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 预训练命令
    pretrain_parser = subparsers.add_parser("pretrain", help="BERT预训练（MLM+NSP）")
    pretrain_parser.add_argument("--output_dir", type=str, default="./bert_pretrained",
                                help="输出目录")
    pretrain_parser.add_argument("--config_file", type=str, default=None,
                                help="配置文件路径")
    pretrain_parser.add_argument("--batch_size", type=int, default=16,
                                help="批次大小")
    pretrain_parser.add_argument("--learning_rate", type=float, default=1e-4,
                                help="学习率")
    pretrain_parser.add_argument("--num_epochs", type=int, default=3,
                                help="训练轮数")
    pretrain_parser.add_argument("--max_seq_length", type=int, default=128,
                                help="最大序列长度")
    pretrain_parser.add_argument("--hidden_size", type=int, default=768,
                                help="隐藏层维度")
    pretrain_parser.add_argument("--num_hidden_layers", type=int, default=12,
                                help="Transformer层数")
    pretrain_parser.add_argument("--num_attention_heads", type=int, default=12,
                                help="注意力头数")
    pretrain_parser.add_argument("--device", type=str, default="auto",
                                help="训练设备")
    pretrain_parser.add_argument("--seed", type=int, default=42,
                                help="随机种子")
    pretrain_parser.add_argument("--max_samples", type=int, default=None,
                                help="最大样本数量（用于测试）")
    pretrain_parser.add_argument("--dataset_name", type=str, default="Salesforce/wikitext",
                                help="数据集名称")
    pretrain_parser.add_argument("--dataset_config", type=str, default="wikitext-103-raw-v1",
                                help="数据集配置")
    
    # 微调命令
    finetune_parser = subparsers.add_parser("finetune", help="BERT微调（分类任务）")
    finetune_parser.add_argument("--pretrained_model_path", type=str, required=True,
                                help="预训练模型路径")
    finetune_parser.add_argument("--output_dir", type=str, default="./bert_fine_tuned",
                                help="输出目录")
    finetune_parser.add_argument("--batch_size", type=int, default=16,
                                help="批次大小")
    finetune_parser.add_argument("--learning_rate", type=float, default=2e-5,
                                help="学习率")
    finetune_parser.add_argument("--num_epochs", type=int, default=3,
                                help="训练轮数")
    finetune_parser.add_argument("--max_samples", type=int, default=None,
                                help="最大样本数量（用于测试）")
    
    # 推理命令
    infer_parser = subparsers.add_parser("infer", help="运行BERT推理")
    infer_parser.add_argument("--model_path", type=str, required=True,
                             help="模型路径")
    infer_parser.add_argument("--mode", type=str, default="mask",
                             choices=["mask", "similarity", "embedding"],
                             help="推理模式")
    infer_parser.add_argument("--text", type=str, default=None,
                             help="要处理的文本")
    infer_parser.add_argument("--text2", type=str, default=None,
                             help="第二个文本（用于相似度计算）")
    infer_parser.add_argument("--tokenizer_name", type=str, default="bert-base-uncased",
                             help="tokenizer名称")
    
    # 快速开始命令
    quick_parser = subparsers.add_parser("quick", help="快速开始（小规模预训练）")
    quick_parser.add_argument("--output_dir", type=str, default="./bert_quick_test",
                             help="输出目录")
    
    # 完整流程命令
    full_parser = subparsers.add_parser("full", help="完整流程（预训练+微调）")
    full_parser.add_argument("--pretrain_output_dir", type=str, default="./bert_pretrained",
                            help="预训练输出目录")
    full_parser.add_argument("--finetune_output_dir", type=str, default="./bert_fine_tuned",
                            help="微调输出目录")
    full_parser.add_argument("--pretrain_epochs", type=int, default=1,
                            help="预训练轮数")
    full_parser.add_argument("--finetune_epochs", type=int, default=3,
                            help="微调轮数")
    full_parser.add_argument("--max_samples", type=int, default=1000,
                            help="最大样本数量")
    
    # 示例命令
    example_parser = subparsers.add_parser("example", help="显示使用示例")
    
    args = parser.parse_args()
    
    if args.command == "pretrain":
        run_pretraining(args)
    elif args.command == "finetune":
        run_fine_tuning(args)
    elif args.command == "infer":
        run_inference(args)
    elif args.command == "quick":
        # 快速预训练配置
        args.batch_size = 8
        args.learning_rate = 1e-4
        args.num_epochs = 1
        args.max_seq_length = 64
        args.hidden_size = 256
        args.num_hidden_layers = 4
        args.num_attention_heads = 4
        args.device = "auto"
        args.seed = 42
        args.max_samples = 1000
        args.config_file = None
        args.dataset_name = "Salesforce/wikitext"
        args.dataset_config = "wikitext-2-raw-v1"
        
        print("=== 快速预训练模式 ===")
        print("使用小规模配置进行快速测试")
        print(f"批次大小: {args.batch_size}")
        print(f"学习率: {args.learning_rate}")
        print(f"训练轮数: {args.num_epochs}")
        print(f"隐藏层维度: {args.hidden_size}")
        print(f"Transformer层数: {args.num_hidden_layers}")
        print(f"最大样本数: {args.max_samples}")
        print()
        
        run_pretraining(args)
    elif args.command == "full":
        print("=== 完整流程：预训练 + 微调 ===")
        
        # 第一步：预训练
        print("第一步：BERT预训练...")
        pretrain_args = argparse.Namespace(
            output_dir=args.pretrain_output_dir,
            batch_size=8,
            learning_rate=1e-4,
            num_epochs=args.pretrain_epochs,
            max_seq_length=128,
            hidden_size=256,
            num_hidden_layers=6,
            num_attention_heads=8,
            device="auto",
            seed=42,
            max_samples=args.max_samples,
            config_file=None,
            dataset_name="Salesforce/wikitext",
            dataset_config="wikitext-2-raw-v1"
        )
        run_pretraining(pretrain_args)
        
        # 第二步：微调
        print("\n第二步：BERT微调...")
        finetune_args = argparse.Namespace(
            pretrained_model_path=f"{args.pretrain_output_dir}/bert_run_*/best_model",
            output_dir=args.finetune_output_dir,
            batch_size=16,
            learning_rate=2e-5,
            num_epochs=args.finetune_epochs,
            max_samples=args.max_samples
        )
        
        # 找到实际的模型路径
        import glob
        model_paths = glob.glob(f"{args.pretrain_output_dir}/bert_run_*/best_model")
        if model_paths:
            finetune_args.pretrained_model_path = model_paths[0]
            run_fine_tuning(finetune_args)
        else:
            print("错误：找不到预训练模型，请检查预训练是否成功完成")
    elif args.command == "example":
        print("=== BERT框架使用示例 ===")
        print()
        print("1. 快速预训练（小规模测试）:")
        print("   python run_bert.py quick")
        print()
        print("2. 完整预训练:")
        print("   python run_bert.py pretrain --num_epochs 3 --batch_size 16")
        print()
        print("3. 微调分类任务:")
        print("   python run_bert.py finetune --pretrained_model_path ./bert_pretrained/best_model")
        print()
        print("4. 完整流程（预训练+微调）:")
        print("   python run_bert.py full --pretrain_epochs 1 --finetune_epochs 3")
        print()
        print("5. 掩码语言模型推理:")
        print("   python run_bert.py infer --model_path ./bert_pretrained/best_model --mode mask --text 'The capital of France is [MASK].'")
        print()
        print("6. 文本相似度计算:")
        print("   python run_bert.py infer --model_path ./bert_pretrained/best_model --mode similarity --text 'I love cats.' --text2 'I adore felines.'")
        print()
        print("注意: 在Mac M1上会自动使用MPS加速")
        print("注意: 预训练包含MLM和NSP两个任务")
        print("注意: 微调使用IMDB情感分析数据集")
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
