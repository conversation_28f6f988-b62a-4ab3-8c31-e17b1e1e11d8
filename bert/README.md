# BERT从零实现框架 - 重构版

这是一个从零实现的BERT（Bidirectional Encoder Representations from Transformers）框架，使用PyTorch构建，专为学习和理解BERT架构而设计。本版本基于Transformer架构重构，提供更清晰的代码结构和完整的功能。

## ✨ 核心特性

### 🏗️ **完整的Transformer架构**
- **基础组件**：MultiHeadSelfAttention、FeedForward、LayerNorm、AddNorm
- **编码器层**：TransformerEncoderLayer（BERT的核心构建块）
- **BERT编码器**：多层Transformer编码器堆叠
- **清晰分离**：Transformer组件与BERT特定组件分离，便于学习

### 🎯 **完整的BERT实现**
- **基础模型**：BertModel（嵌入层 + 编码器 + 池化层）
- **预训练模型**：BertForPreTraining（MLM + NSP两个任务）
- **MLM模型**：BertForMaskedLM（仅掩码语言模型）
- **分类模型**：BertForSequenceClassification（微调分类任务）

### 📚 **两个预训练任务**
- **MLM（Masked Language Model）**：掩码语言模型，预测被掩码的词
- **NSP（Next Sentence Prediction）**：下一句预测，判断两个句子是否连续

### 🔧 **微调功能**
- **情感分析**：支持IMDB数据集的情感分类微调
- **权重加载**：从预训练模型加载权重进行微调
- **性能评估**：准确率、精确率、召回率、F1分数

### 🚀 **其他特性**
- **Mac M1优化**：自动检测并使用MPS加速
- **HuggingFace集成**：支持Salesforce/wikitext数据集和tokenizer
- **Pydantic配置**：类型安全的配置管理
- **完善日志**：详细的训练和推理日志
- **推理功能**：掩码预测、文本相似度、嵌入提取

## 📁 项目结构

```
bert/
├── 🏗️ Transformer基础组件
│   ├── transformer_components.py  # 多头注意力、前馈网络、AddNorm等
│   ├── bert_embeddings.py        # BERT嵌入层和池化层
│   └── bert_encoder.py           # BERT编码器（基于Transformer）
│
├── 🎯 BERT模型实现
│   ├── model.py                  # 完整BERT模型（基础、预训练、分类）
│   └── bert_heads.py             # 预训练头（MLM、NSP、分类）
│
├── 📊 数据处理
│   ├── bert_data_loader.py       # 支持MLM+NSP的数据加载器
│   └── data_loader.py            # 原始数据加载器（兼容）
│
├── 🚀 训练和微调
│   ├── trainer.py                # 预训练训练器
│   ├── fine_tuning.py            # 微调训练器
│   └── main.py                   # 主训练入口
│
├── 🔧 工具和配置
│   ├── config.py                 # Pydantic配置定义
│   ├── utils.py                  # 工具函数和日志
│   └── inference.py              # 推理和测试
│
├── 🎮 运行脚本
│   ├── run_bert.py               # 新版运行脚本（支持预训练+微调）
│   ├── run.py                    # 原版运行脚本（兼容）
│   └── test_setup.py             # 功能测试脚本
│
└── 📚 文档
    ├── README.md                 # 主要文档
    ├── GETTING_STARTED.md        # 快速入门
    └── requirements.txt          # 依赖包列表
```

## 安装依赖

```bash
cd bert
pip install -r requirements.txt
```

## 🚀 快速开始

### 1. 环境准备

```bash
cd bert
pip install -r requirements.txt
```

### 2. 快速预训练（推荐新手）

```bash
# 快速预训练（小规模，约2分钟）
python run_bert.py quick
```

### 3. 完整流程（预训练 + 微调）

```bash
# 完整流程：预训练 + 情感分析微调
python run_bert.py full --pretrain_epochs 1 --finetune_epochs 3
```

### 4. 分步执行

```bash
# 第一步：BERT预训练（MLM + NSP）
python run_bert.py pretrain --num_epochs 3 --batch_size 16

# 第二步：分类任务微调
python run_bert.py finetune \
    --pretrained_model_path ./bert_pretrained/best_model \
    --num_epochs 3

# 第三步：模型推理测试
python run_bert.py infer \
    --model_path ./bert_pretrained/best_model \
    --mode mask \
    --text "The capital of France is [MASK]."
```

## 推理使用

### 掩码语言模型

```bash
python run.py infer \
    --model_path ./bert_output/best_model \
    --mode mask \
    --text "The capital of France is [MASK]."
```

### 文本相似度计算

```bash
python run.py infer \
    --model_path ./bert_output/best_model \
    --mode similarity \
    --text "I love cats." \
    --text2 "I adore felines."
```

### 获取文本嵌入

```bash
python run.py infer \
    --model_path ./bert_output/best_model \
    --mode embedding \
    --text "This is a test sentence."
```

## 配置说明

### 模型配置 (BertConfig)

- `vocab_size`: 词汇表大小（默认：30522）
- `hidden_size`: 隐藏层维度（默认：768）
- `num_hidden_layers`: Transformer层数（默认：12）
- `num_attention_heads`: 注意力头数（默认：12）
- `intermediate_size`: 前馈网络中间层维度（默认：3072）
- `max_position_embeddings`: 最大位置编码长度（默认：512）

### 训练配置 (TrainingConfig)

- `batch_size`: 批次大小（默认：16）
- `learning_rate`: 学习率（默认：5e-5）
- `num_epochs`: 训练轮数（默认：3）
- `warmup_steps`: 学习率预热步数（默认：1000）
- `max_seq_length`: 最大序列长度（默认：128）

## 架构详解

### 1. BERT嵌入层 (BertEmbeddings)
- 词嵌入 (Word Embeddings)
- 位置嵌入 (Position Embeddings)
- 类型嵌入 (Token Type Embeddings)
- LayerNorm + Dropout

### 2. 多头自注意力 (BertSelfAttention)
- Query、Key、Value线性变换
- 缩放点积注意力
- 多头并行计算
- 注意力dropout

### 3. Transformer层 (BertLayer)
- 自注意力机制
- 残差连接
- 前馈网络
- LayerNorm

### 4. BERT编码器 (BertEncoder)
- 多层Transformer堆叠
- 支持输出中间隐藏状态
- 支持输出注意力权重

### 5. MLM预训练头 (BertLMPredictionHead)
- 线性变换 + 激活函数
- LayerNorm
- 词汇表投影
- 权重共享

## 训练数据

默认使用HuggingFace的`Salesforce/wikitext`数据集，这是一个高质量的英文维基百科文本数据集，非常适合语言模型预训练。

支持的数据集配置：
- `wikitext-103-raw-v1`: 大规模数据集（推荐用于完整训练）
- `wikitext-2-raw-v1`: 小规模数据集（推荐用于快速测试）
- `wikitext-103-v1`: 预处理版本
- `wikitext-2-v1`: 预处理版本

## 设备支持

- **Mac M1/M2**: 自动使用MPS加速
- **NVIDIA GPU**: 自动使用CUDA
- **CPU**: 兜底支持

## 日志和监控

训练过程中会记录：
- 训练损失和验证损失
- 学习率变化
- 内存使用情况
- 训练时间
- 模型参数数量

## 模型保存

训练过程中会保存：
- 最佳模型（基于验证损失）
- 最终模型
- 训练检查点
- 配置文件
- 训练历史

## 示例输出

```
2024-01-01 10:00:00 - BERT - INFO - BERT模型初始化完成，参数数量: 109,482,240
2024-01-01 10:00:01 - BERT - INFO - 训练器初始化完成
2024-01-01 10:00:01 - BERT - INFO - 设备: mps
2024-01-01 10:00:01 - BERT - INFO - 开始训练
Epoch 1/3: 100%|██████████| 1000/1000 [05:23<00:00, 3.09it/s, loss=4.2341, lr=5.00e-05]
2024-01-01 10:05:24 - BERT - INFO - Epoch 1 完成
2024-01-01 10:05:24 - BERT - INFO - 训练损失: 4.2341
2024-01-01 10:05:24 - BERT - INFO - 验证损失: 3.9876
```

## 学习建议

1. **从小规模开始**：使用`python run.py quick`进行快速测试
2. **理解架构**：仔细阅读`model.py`中的注释
3. **观察训练过程**：关注损失变化和学习率调度
4. **实验配置**：尝试不同的模型大小和训练参数
5. **测试推理**：使用不同的文本测试模型效果

## 注意事项

- 首次运行会下载数据集和tokenizer，需要网络连接
- Mac M1用户确保PyTorch版本支持MPS
- 大模型训练需要足够的内存和存储空间
- 建议使用虚拟环境管理依赖

## 故障排除

1. **内存不足**：减小batch_size或模型大小
2. **MPS错误**：设置`--device cpu`使用CPU训练
3. **数据加载失败**：检查网络连接或使用本地数据
4. **依赖冲突**：使用虚拟环境重新安装依赖

## 完整使用示例

### 1. 快速开始（推荐）

```bash
# 进入项目目录
cd bert

# 运行快速训练（使用小规模配置和数据集）
python run.py quick

# 训练完成后测试模型
python run.py infer --model_path ./bert_quick_test/bert_run_*/best_model --mode mask --text "The capital of France is [MASK]."
```

### 2. 完整训练流程

```bash
# 使用完整配置训练
python run.py train \
    --num_epochs 3 \
    --batch_size 16 \
    --learning_rate 5e-5 \
    --hidden_size 768 \
    --num_hidden_layers 12 \
    --dataset_config wikitext-103-raw-v1

# 或者直接使用main.py
python main.py \
    --output_dir ./my_bert_model \
    --dataset_name Salesforce/wikitext \
    --dataset_config wikitext-103-raw-v1 \
    --batch_size 16 \
    --num_epochs 3 \
    --max_seq_length 128
```

### 3. 模型推理示例

```bash
# 掩码语言模型预测
python inference.py \
    --model_path ./bert_output/best_model \
    --mode mask \
    --text "The weather today is [MASK]."

# 文本相似度计算
python inference.py \
    --model_path ./bert_output/best_model \
    --mode similarity \
    --text "Machine learning is powerful." \
    --text2 "AI technology is amazing."

# 获取文本嵌入
python inference.py \
    --model_path ./bert_output/best_model \
    --mode embedding \
    --text "This is a sample sentence for embedding."
```

### 4. 训练结果示例

快速训练（1个epoch，1000个样本）的典型结果：
```
训练损失: 9.9483 → 验证损失: 9.5092
参数数量: 15,469,882
训练时间: ~1分钟（Mac M1）
内存使用: ~247MB GPU + 695MB CPU
```

### 5. 自定义配置

创建配置文件 `my_config.json`：
```json
{
  "model": {
    "vocab_size": 30522,
    "hidden_size": 512,
    "num_hidden_layers": 8,
    "num_attention_heads": 8,
    "max_position_embeddings": 256
  },
  "training": {
    "batch_size": 32,
    "learning_rate": 1e-4,
    "num_epochs": 5,
    "dataset_name": "Salesforce/wikitext",
    "dataset_config": "wikitext-2-raw-v1"
  }
}
```

使用自定义配置：
```bash
python main.py --config_file my_config.json
```

## 性能基准

| 配置 | 参数量 | 训练时间/epoch | 内存使用 | 验证损失 |
|------|--------|----------------|----------|----------|
| 快速测试 | 15.5M | ~1分钟 | 247MB | 9.51 |
| 标准配置 | 110M | ~30分钟 | 2GB | ~4.5 |
| 大型配置 | 340M | ~2小时 | 8GB | ~3.2 |

*基于Mac M1 Pro测试结果

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
