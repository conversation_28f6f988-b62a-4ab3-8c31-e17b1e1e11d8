"""
BERT训练主入口文件
一键运行BERT预训练
"""

import os
import argparse
import json
import logging
from pathlib import Path
import torch

from config import BertConfig, TrainingConfig, DataConfig, LoggingConfig, get_device
from model import BertForMaskedLM
from data_loader import create_data_loader, get_vocab_size_from_tokenizer
from trainer import Bert<PERSON>rainer
from utils import setup_logging, set_seed, save_config, create_output_dir, count_parameters

logger = logging.getLogger('BERT')


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="BERT预训练")

    # 基础参数
    parser.add_argument("--output_dir", type=str, default="./bert_output",
                       help="输出目录")
    parser.add_argument("--config_file", type=str, default=None,
                       help="配置文件路径")

    # 模型参数
    parser.add_argument("--hidden_size", type=int, default=768,
                       help="隐藏层维度")
    parser.add_argument("--num_hidden_layers", type=int, default=12,
                       help="Transformer层数")
    parser.add_argument("--num_attention_heads", type=int, default=12,
                       help="注意力头数")
    parser.add_argument("--max_position_embeddings", type=int, default=512,
                       help="最大位置编码长度")

    # 训练参数
    parser.add_argument("--batch_size", type=int, default=16,
                       help="批次大小")
    parser.add_argument("--learning_rate", type=float, default=5e-5,
                       help="学习率")
    parser.add_argument("--num_epochs", type=int, default=3,
                       help="训练轮数")
    parser.add_argument("--warmup_steps", type=int, default=1000,
                       help="学习率预热步数")
    parser.add_argument("--max_seq_length", type=int, default=128,
                       help="最大序列长度")

    # 数据参数
    parser.add_argument("--dataset_name", type=str, default="roneneldan/TinyStories-33M",
                       help="数据集名称")
    parser.add_argument("--tokenizer_name", type=str, default="bert-base-uncased",
                       help="tokenizer名称")
    parser.add_argument("--max_samples", type=int, default=None,
                       help="最大样本数量（用于测试）")

    # 其他参数
    parser.add_argument("--seed", type=int, default=42,
                       help="随机种子")
    parser.add_argument("--device", type=str, default="auto",
                       help="训练设备")
    parser.add_argument("--log_level", type=str, default="INFO",
                       help="日志级别")
    parser.add_argument("--save_steps", type=int, default=1000,
                       help="模型保存步数间隔")
    parser.add_argument("--logging_steps", type=int, default=100,
                       help="日志记录步数间隔")

    return parser.parse_args()


def load_config_from_file(config_file: str) -> dict:
    """从文件加载配置"""
    with open(config_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def create_config_from_args(args) -> dict:
    """从命令行参数创建配置"""
    # 获取tokenizer词汇表大小
    vocab_size = get_vocab_size_from_tokenizer(args.tokenizer_name)

    # 创建配置
    config = {
        "model": BertConfig(
            vocab_size=vocab_size,
            hidden_size=args.hidden_size,
            num_hidden_layers=args.num_hidden_layers,
            num_attention_heads=args.num_attention_heads,
            max_position_embeddings=args.max_position_embeddings
        ),
        "training": TrainingConfig(
            batch_size=args.batch_size,
            learning_rate=args.learning_rate,
            num_epochs=args.num_epochs,
            warmup_steps=args.warmup_steps,
            max_seq_length=args.max_seq_length,
            dataset_name=args.dataset_name,
            output_dir=args.output_dir,
            device=args.device,
            save_steps=args.save_steps,
            logging_steps=args.logging_steps
        ),
        "data": DataConfig(
            tokenizer_name=args.tokenizer_name,
            max_length=args.max_seq_length
        ),
        "logging": LoggingConfig(
            log_level=args.log_level,
            log_file=os.path.join(args.output_dir, "training.log")
        )
    }

    return config


def main():
    """主函数"""
    # 解析参数
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 创建输出目录
    output_dir = create_output_dir(args.output_dir)
    args.output_dir = output_dir

    # 加载或创建配置
    if args.config_file and os.path.exists(args.config_file):
        logger.info(f"从文件加载配置: {args.config_file}")
        config_dict = load_config_from_file(args.config_file)
        config = {
            "model": BertConfig(**config_dict["model"]),
            "training": TrainingConfig(**config_dict["training"]),
            "data": DataConfig(**config_dict["data"]),
            "logging": LoggingConfig(**config_dict["logging"])
        }
    else:
        logger.info("从命令行参数创建配置")
        config = create_config_from_args(args)

    # 设置日志
    setup_logging(config["logging"])

    # 保存配置
    config_save_path = os.path.join(output_dir, "config.json")
    save_config(config, config_save_path)
    logger.info(f"配置已保存到: {config_save_path}")

    # 打印配置信息
    logger.info("=== 配置信息 ===")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"设备: {get_device()}")
    logger.info(f"模型配置: {config['model']}")
    logger.info(f"训练配置: {config['training']}")
    logger.info(f"数据配置: {config['data']}")

    try:
        # 创建数据加载器
        logger.info("创建训练数据加载器...")
        train_dataloader, tokenizer = create_data_loader(
            config["data"],
            config["training"],
            split="train",
            max_samples=args.max_samples
        )

        # 创建验证数据加载器（如果有验证集）
        val_dataloader = None
        try:
            logger.info("创建验证数据加载器...")
            val_dataloader, _ = create_data_loader(
                config["data"],
                config["training"],
                split="validation",
                max_samples=args.max_samples // 10 if args.max_samples else None
            )
        except Exception as e:
            logger.warning(f"无法创建验证数据加载器: {e}")
            logger.info("将仅使用训练数据")

        # 创建模型
        logger.info("创建BERT模型...")
        model = BertForMaskedLM(config["model"])

        # 打印模型信息
        param_count = count_parameters(model)
        logger.info(f"模型参数数量: {param_count:,}")

        # 创建训练器
        logger.info("创建训练器...")
        trainer = BertTrainer(
            model=model,
            train_dataloader=train_dataloader,
            val_dataloader=val_dataloader,
            training_config=config["training"],
            tokenizer=tokenizer
        )

        # 开始训练
        logger.info("开始训练...")
        training_history = trainer.train()

        logger.info("训练完成！")
        logger.info(f"训练结果保存在: {output_dir}")

        # 打印训练总结
        if training_history["train_loss"]:
            final_train_loss = training_history["train_loss"][-1]
            logger.info(f"最终训练损失: {final_train_loss:.4f}")

        if training_history["val_loss"]:
            final_val_loss = training_history["val_loss"][-1]
            logger.info(f"最终验证损失: {final_val_loss:.4f}")

    except Exception as e:
        logger.error(f"训练过程中发生错误: {e}")
        raise

    logger.info("程序执行完成")


if __name__ == "__main__":
    main()
