"""
BERT训练器
包含完整的训练逻辑、优化器配置、学习率调度等
"""

import torch
import torch.nn as nn
from torch.optim import AdamW
from torch.optim.lr_scheduler import LinearLR, SequentialLR, ConstantLR
import os
import json
import logging
from typing import Dict, Any, Optional, Tuple
from tqdm import tqdm
import time
from pathlib import Path

from config import BertConfig, TrainingConfig, get_device
from model import BertForMaskedLM
from utils import MetricsTracker, Timer, get_memory_usage, format_time

logger = logging.getLogger('BERT')


class BertTrainer:
    """BERT训练器"""
    
    def __init__(
        self,
        model: BertForMaskedLM,
        train_dataloader,
        val_dataloader,
        training_config: TrainingConfig,
        tokenizer=None
    ):
        self.model = model
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader
        self.config = training_config
        self.tokenizer = tokenizer
        
        # 设备配置
        self.device = self._setup_device()
        self.model.to(self.device)
        
        # 优化器和调度器
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        
        # 指标跟踪
        self.metrics_tracker = MetricsTracker()
        
        # 训练状态
        self.global_step = 0
        self.epoch = 0
        self.best_val_loss = float('inf')
        
        # 输出目录
        self.output_dir = Path(training_config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"训练器初始化完成")
        logger.info(f"设备: {self.device}")
        logger.info(f"模型参数数量: {self.model.count_parameters():,}")
        logger.info(f"训练批次数量: {len(self.train_dataloader)}")
        if self.val_dataloader:
            logger.info(f"验证批次数量: {len(self.val_dataloader)}")
    
    def _setup_device(self) -> torch.device:
        """设置训练设备"""
        if self.config.device == "auto":
            device = get_device()
        else:
            device = torch.device(self.config.device)
        
        logger.info(f"使用设备: {device}")
        
        # 设置MPS优化（如果使用Mac M1）
        if device.type == "mps":
            logger.info("检测到MPS设备，启用相关优化")
        
        return device
    
    def _create_optimizer(self) -> torch.optim.Optimizer:
        """创建优化器"""
        # 分离权重衰减参数
        no_decay = ["bias", "LayerNorm.weight"]
        optimizer_grouped_parameters = [
            {
                "params": [p for n, p in self.model.named_parameters() 
                          if not any(nd in n for nd in no_decay)],
                "weight_decay": self.config.weight_decay,
            },
            {
                "params": [p for n, p in self.model.named_parameters() 
                          if any(nd in n for nd in no_decay)],
                "weight_decay": 0.0,
            },
        ]
        
        optimizer = AdamW(
            optimizer_grouped_parameters,
            lr=self.config.learning_rate,
            eps=self.config.adam_epsilon,
            betas=(self.config.adam_beta1, self.config.adam_beta2)
        )
        
        logger.info(f"创建AdamW优化器，学习率: {self.config.learning_rate}")
        return optimizer
    
    def _create_scheduler(self) -> torch.optim.lr_scheduler._LRScheduler:
        """创建学习率调度器"""
        total_steps = len(self.train_dataloader) * self.config.num_epochs
        
        # 预热调度器
        warmup_scheduler = LinearLR(
            self.optimizer,
            start_factor=0.1,
            end_factor=1.0,
            total_iters=self.config.warmup_steps
        )
        
        # 线性衰减调度器
        decay_scheduler = LinearLR(
            self.optimizer,
            start_factor=1.0,
            end_factor=0.0,
            total_iters=total_steps - self.config.warmup_steps
        )
        
        # 组合调度器
        scheduler = SequentialLR(
            self.optimizer,
            schedulers=[warmup_scheduler, decay_scheduler],
            milestones=[self.config.warmup_steps]
        )
        
        logger.info(f"创建学习率调度器，总步数: {total_steps}, 预热步数: {self.config.warmup_steps}")
        return scheduler
    
    def train_epoch(self) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        epoch_metrics = MetricsTracker()
        
        progress_bar = tqdm(
            self.train_dataloader,
            desc=f"Epoch {self.epoch + 1}/{self.config.num_epochs}",
            leave=False
        )
        
        for step, batch in enumerate(progress_bar):
            # 移动数据到设备
            batch = {k: v.to(self.device) for k, v in batch.items()}
            
            # 前向传播
            outputs = self.model(**batch)
            loss = outputs["loss"]
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
            
            # 优化器步骤
            self.optimizer.step()
            self.scheduler.step()
            self.optimizer.zero_grad()
            
            # 更新指标
            epoch_metrics.update(
                loss=loss.item(),
                learning_rate=self.scheduler.get_last_lr()[0]
            )
            
            self.global_step += 1
            
            # 更新进度条
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'lr': f"{self.scheduler.get_last_lr()[0]:.2e}"
            })
            
            # 记录日志
            if self.global_step % self.config.logging_steps == 0:
                self._log_training_metrics(epoch_metrics)
            
            # 保存检查点
            if self.global_step % self.config.save_steps == 0:
                self._save_checkpoint()
        
        return epoch_metrics.get_summary()
    
    def validate(self) -> Dict[str, float]:
        """验证模型"""
        if not self.val_dataloader:
            return {}
        
        self.model.eval()
        val_metrics = MetricsTracker()
        
        with torch.no_grad():
            for batch in tqdm(self.val_dataloader, desc="验证", leave=False):
                # 移动数据到设备
                batch = {k: v.to(self.device) for k, v in batch.items()}
                
                # 前向传播
                outputs = self.model(**batch)
                loss = outputs["loss"]
                
                # 更新指标
                val_metrics.update(val_loss=loss.item())
        
        return val_metrics.get_summary()
    
    def train(self) -> Dict[str, Any]:
        """完整训练流程"""
        logger.info("开始训练")
        training_start_time = time.time()
        
        training_history = {
            "train_loss": [],
            "val_loss": [],
            "learning_rate": [],
            "epochs": []
        }
        
        for epoch in range(self.config.num_epochs):
            self.epoch = epoch
            epoch_start_time = time.time()
            
            # 训练一个epoch
            train_metrics = self.train_epoch()
            
            # 验证
            val_metrics = self.validate()
            
            # 记录历史
            training_history["train_loss"].append(train_metrics["loss"]["average"])
            training_history["learning_rate"].append(train_metrics["learning_rate"]["current"])
            training_history["epochs"].append(epoch + 1)
            
            if val_metrics:
                val_loss = val_metrics["val_loss"]["average"]
                training_history["val_loss"].append(val_loss)
                
                # 保存最佳模型
                if val_loss < self.best_val_loss:
                    self.best_val_loss = val_loss
                    self._save_best_model()
            
            # 记录epoch总结
            epoch_time = time.time() - epoch_start_time
            logger.info(f"Epoch {epoch + 1} 完成")
            logger.info(f"训练损失: {train_metrics['loss']['average']:.4f}")
            if val_metrics:
                logger.info(f"验证损失: {val_metrics['val_loss']['average']:.4f}")
            logger.info(f"学习率: {train_metrics['learning_rate']['current']:.2e}")
            logger.info(f"用时: {format_time(epoch_time)}")
            logger.info(f"内存使用: {get_memory_usage()}")
        
        # 训练完成
        total_time = time.time() - training_start_time
        logger.info(f"训练完成，总用时: {format_time(total_time)}")
        
        # 保存最终模型
        self._save_final_model()
        
        # 保存训练历史
        self._save_training_history(training_history)
        
        return training_history
    
    def _log_training_metrics(self, metrics: MetricsTracker):
        """记录训练指标"""
        memory_info = get_memory_usage()
        logger.info(
            f"Step {self.global_step}: "
            f"loss={metrics.metrics.get('loss', 0):.4f}, "
            f"lr={metrics.metrics.get('learning_rate', 0):.2e}, "
            f"memory={memory_info.get('cpu_memory_mb', 0):.1f}MB"
        )
    
    def _save_checkpoint(self):
        """保存训练检查点"""
        checkpoint_dir = self.output_dir / f"checkpoint-{self.global_step}"
        checkpoint_dir.mkdir(exist_ok=True)
        
        # 保存模型状态
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'global_step': self.global_step,
            'epoch': self.epoch,
            'best_val_loss': self.best_val_loss,
        }, checkpoint_dir / "pytorch_model.bin")
        
        # 保存配置
        self.model.config.dict()
        with open(checkpoint_dir / "config.json", 'w') as f:
            json.dump(self.model.config.dict(), f, indent=2)
        
        logger.info(f"保存检查点: {checkpoint_dir}")
    
    def _save_best_model(self):
        """保存最佳模型"""
        best_model_dir = self.output_dir / "best_model"
        best_model_dir.mkdir(exist_ok=True)
        
        torch.save(self.model.state_dict(), best_model_dir / "pytorch_model.bin")
        
        with open(best_model_dir / "config.json", 'w') as f:
            json.dump(self.model.config.dict(), f, indent=2)
        
        logger.info(f"保存最佳模型，验证损失: {self.best_val_loss:.4f}")
    
    def _save_final_model(self):
        """保存最终模型"""
        final_model_dir = self.output_dir / "final_model"
        final_model_dir.mkdir(exist_ok=True)
        
        torch.save(self.model.state_dict(), final_model_dir / "pytorch_model.bin")
        
        with open(final_model_dir / "config.json", 'w') as f:
            json.dump(self.model.config.dict(), f, indent=2)
        
        logger.info("保存最终模型")
    
    def _save_training_history(self, history: Dict[str, Any]):
        """保存训练历史"""
        with open(self.output_dir / "training_history.json", 'w') as f:
            json.dump(history, f, indent=2)
        
        logger.info("保存训练历史")
