# BERT架构详解

本文档详细解释了我们从零实现的BERT框架的架构设计。

## 🏗️ 整体架构

我们的BERT实现基于清晰的分层架构，从底层的Transformer组件到顶层的任务特定模型：

```
┌─────────────────────────────────────────────────────────────┐
│                    任务特定模型层                              │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ BertForMaskedLM │ │BertForPreTraining│ │BertForSequence  │ │
│  │     (MLM)       │ │   (MLM + NSP)   │ │ Classification  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      BERT核心模型                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   BertModel                             │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │BertEmbeddings│ │ BertEncoder │ │ BertPooler  │       │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Transformer基础组件                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │MultiHeadSelf    │ │   FeedForward   │ │    AddNorm      │ │
│  │   Attention     │ │                 │ │                 │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐                   │
│  │   LayerNorm     │ │TransformerEncoder│                   │
│  │                 │ │     Layer       │                   │
│  └─────────────────┘ └─────────────────┘                   │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件详解

### 1. Transformer基础组件 (`transformer_components.py`)

#### MultiHeadSelfAttention
- **功能**：实现多头自注意力机制
- **特点**：专门为BERT设计，Q、K、V都来自同一输入
- **关键方法**：
  - `scaled_dot_product_attention()`: 缩放点积注意力
  - `forward()`: 支持注意力掩码和权重输出

#### FeedForward
- **功能**：位置前馈网络
- **结构**：Linear -> Activation -> Dropout -> Linear
- **激活函数**：支持GELU和ReLU

#### AddNorm
- **功能**：残差连接 + 层归一化
- **实现**：LayerNorm(x + Sublayer(x))
- **作用**：稳定训练，缓解梯度消失

#### TransformerEncoderLayer
- **功能**：单个Transformer编码器层
- **结构**：Self-Attention + AddNorm + FeedForward + AddNorm
- **输出**：隐藏状态和注意力权重

### 2. BERT嵌入层 (`bert_embeddings.py`)

#### BertEmbeddings
- **词嵌入**：将token ID转换为向量表示
- **位置嵌入**：编码token在序列中的位置信息
- **类型嵌入**：区分句子A和句子B（用于NSP任务）
- **LayerNorm + Dropout**：归一化和正则化

#### BertPooler
- **功能**：将序列表示转换为句子表示
- **方法**：取[CLS] token的隐藏状态 -> Linear -> Tanh
- **用途**：分类任务的句子级表示

### 3. BERT编码器 (`bert_encoder.py`)

#### BertEncoder
- **功能**：多层Transformer编码器堆叠
- **特性**：
  - 支持梯度检查点（节省内存）
  - 可输出中间隐藏状态
  - 可输出注意力权重
  - 支持分块处理（大序列）

### 4. 预训练头 (`bert_heads.py`)

#### BertLMPredictionHead (MLM)
- **变换层**：Linear -> GELU -> LayerNorm
- **输出层**：投影到词汇表大小
- **权重共享**：与输入嵌入层共享权重

#### BertNSPHead (NSP)
- **功能**：二分类头，判断句子关系
- **输入**：池化输出（[CLS] token表示）
- **输出**：2维logits（IsNext/NotNext）

#### BertClassificationHead
- **功能**：通用分类头
- **结构**：Dropout -> Linear
- **用途**：微调任务（如情感分析）

## 🎯 模型变体

### 1. BertModel（基础模型）
```python
BertModel = BertEmbeddings + BertEncoder + BertPooler
```
- **用途**：其他模型的基础
- **输出**：序列表示 + 池化表示

### 2. BertForMaskedLM（MLM预训练）
```python
BertForMaskedLM = BertModel + BertLMPredictionHead
```
- **任务**：掩码语言模型
- **损失**：交叉熵损失（仅计算掩码位置）

### 3. BertForPreTraining（完整预训练）
```python
BertForPreTraining = BertModel + BertLMPredictionHead + BertNSPHead
```
- **任务**：MLM + NSP
- **损失**：MLM损失 + NSP损失

### 4. BertForSequenceClassification（分类微调）
```python
BertForSequenceClassification = BertModel + BertClassificationHead
```
- **任务**：序列分类（如情感分析）
- **损失**：交叉熵损失

## 📊 数据流

### 预训练数据流
```
原始文本 -> 句子对构造 -> Tokenization -> MLM掩码 -> NSP标签
    ↓
[input_ids, token_type_ids, attention_mask, labels, next_sentence_label]
    ↓
BertForPreTraining -> [MLM_logits, NSP_logits] -> 损失计算
```

### 微调数据流
```
分类文本 -> Tokenization -> [input_ids, attention_mask, labels]
    ↓
BertForSequenceClassification -> classification_logits -> 损失计算
```

### 推理数据流
```
输入文本 -> Tokenization -> 模型前向传播 -> 后处理 -> 结果输出
```

## 🔄 训练流程

### 预训练流程
1. **数据准备**：加载WikiText数据集
2. **数据处理**：构造句子对，应用MLM掩码
3. **模型训练**：同时优化MLM和NSP任务
4. **模型保存**：保存最佳模型权重

### 微调流程
1. **权重加载**：从预训练模型加载BERT权重
2. **任务适配**：添加分类头
3. **数据准备**：加载IMDB情感分析数据
4. **微调训练**：较小学习率训练分类头
5. **性能评估**：计算准确率、F1等指标

## 🎨 设计亮点

### 1. 模块化设计
- **组件分离**：Transformer组件独立，可复用
- **层次清晰**：从基础组件到完整模型
- **易于扩展**：可轻松添加新的任务头

### 2. 代码质量
- **类型注解**：使用Pydantic确保类型安全
- **文档完善**：每个函数都有详细注释
- **错误处理**：完善的异常处理机制

### 3. 学习友好
- **渐进式**：从简单组件到复杂模型
- **可视化**：清晰的架构图和数据流
- **实践性**：完整的训练和推理流程

这个架构设计既保持了BERT的完整性，又提供了清晰的学习路径，是理解Transformer和BERT架构的理想选择。
