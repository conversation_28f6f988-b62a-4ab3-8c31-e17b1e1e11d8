"""
BERT预训练头实现
包含MLM（Masked Language Model）和NSP（Next Sentence Prediction）两个预训练任务
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
from transformer_components import LayerNorm


class BertLMPredictionHead(nn.Module):
    """
    BERT语言模型预测头 - 用于MLM任务
    """
    
    def __init__(
        self, 
        hidden_size: int, 
        vocab_size: int, 
        layer_norm_eps: float = 1e-12,
        hidden_act: str = "gelu"
    ):
        """
        初始化MLM预测头
        
        Args:
            hidden_size: 隐藏层维度
            vocab_size: 词汇表大小
            layer_norm_eps: LayerNorm的epsilon值
            hidden_act: 激活函数类型
        """
        super().__init__()
        
        # 变换层：hidden_size -> hidden_size
        self.transform = BertPredictionHeadTransform(hidden_size, layer_norm_eps, hidden_act)
        
        # 输出层：hidden_size -> vocab_size
        # 注意：这里的权重通常与输入嵌入层的权重共享
        self.decoder = nn.Linear(hidden_size, vocab_size, bias=False)
        
        # 输出偏置
        self.bias = nn.Parameter(torch.zeros(vocab_size))
        
        # 需要将bias赋值给decoder.bias以便权重共享时正确处理
        self.decoder.bias = self.bias
    
    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            hidden_states: 编码器输出, shape: (batch_size, seq_len, hidden_size)
        
        Returns:
            prediction_scores: 预测分数, shape: (batch_size, seq_len, vocab_size)
        """
        # 通过变换层
        hidden_states = self.transform(hidden_states)
        
        # 通过输出层得到词汇表上的分数
        hidden_states = self.decoder(hidden_states)
        
        return hidden_states


class BertPredictionHeadTransform(nn.Module):
    """
    BERT预测头的变换层
    """
    
    def __init__(self, hidden_size: int, layer_norm_eps: float = 1e-12, hidden_act: str = "gelu"):
        """
        初始化变换层
        
        Args:
            hidden_size: 隐藏层维度
            layer_norm_eps: LayerNorm的epsilon值
            hidden_act: 激活函数类型
        """
        super().__init__()
        
        self.dense = nn.Linear(hidden_size, hidden_size)
        
        # 激活函数
        if hidden_act == "gelu":
            self.transform_act_fn = F.gelu
        elif hidden_act == "relu":
            self.transform_act_fn = F.relu
        elif hidden_act == "tanh":
            self.transform_act_fn = torch.tanh
        else:
            raise ValueError(f"不支持的激活函数: {hidden_act}")
        
        self.LayerNorm = LayerNorm(hidden_size, eps=layer_norm_eps)
    
    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            hidden_states: 输入隐藏状态
        
        Returns:
            变换后的隐藏状态
        """
        hidden_states = self.dense(hidden_states)
        hidden_states = self.transform_act_fn(hidden_states)
        hidden_states = self.LayerNorm(hidden_states)
        return hidden_states


class BertLMHead(nn.Module):
    """
    BERT语言模型头 - MLM任务的完整实现
    """
    
    def __init__(
        self, 
        hidden_size: int, 
        vocab_size: int, 
        layer_norm_eps: float = 1e-12,
        hidden_act: str = "gelu"
    ):
        """
        初始化语言模型头
        """
        super().__init__()
        self.predictions = BertLMPredictionHead(hidden_size, vocab_size, layer_norm_eps, hidden_act)
    
    def forward(self, sequence_output: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            sequence_output: 序列输出, shape: (batch_size, seq_len, hidden_size)
        
        Returns:
            prediction_scores: 预测分数, shape: (batch_size, seq_len, vocab_size)
        """
        prediction_scores = self.predictions(sequence_output)
        return prediction_scores


class BertNSPHead(nn.Module):
    """
    BERT下一句预测头 - 用于NSP任务
    """
    
    def __init__(self, hidden_size: int):
        """
        初始化NSP预测头
        
        Args:
            hidden_size: 隐藏层维度
        """
        super().__init__()
        
        # NSP是一个二分类任务：IsNext (1) 或 NotNext (0)
        self.seq_relationship = nn.Linear(hidden_size, 2)
    
    def forward(self, pooled_output: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            pooled_output: 池化输出（通常是[CLS] token的表示）, shape: (batch_size, hidden_size)
        
        Returns:
            seq_relationship_scores: NSP分数, shape: (batch_size, 2)
        """
        seq_relationship_score = self.seq_relationship(pooled_output)
        return seq_relationship_score


class BertPreTrainingHeads(nn.Module):
    """
    BERT预训练头 - 包含MLM和NSP两个任务
    """
    
    def __init__(
        self, 
        hidden_size: int, 
        vocab_size: int, 
        layer_norm_eps: float = 1e-12,
        hidden_act: str = "gelu"
    ):
        """
        初始化预训练头
        
        Args:
            hidden_size: 隐藏层维度
            vocab_size: 词汇表大小
            layer_norm_eps: LayerNorm的epsilon值
            hidden_act: 激活函数类型
        """
        super().__init__()
        
        # MLM头
        self.predictions = BertLMPredictionHead(hidden_size, vocab_size, layer_norm_eps, hidden_act)
        
        # NSP头
        self.seq_relationship = nn.Linear(hidden_size, 2)
    
    def forward(
        self, 
        sequence_output: torch.Tensor, 
        pooled_output: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            sequence_output: 序列输出, shape: (batch_size, seq_len, hidden_size)
            pooled_output: 池化输出, shape: (batch_size, hidden_size)
        
        Returns:
            (prediction_scores, seq_relationship_score): MLM和NSP的预测分数
        """
        # MLM预测
        prediction_scores = self.predictions(sequence_output)
        
        # NSP预测
        seq_relationship_score = self.seq_relationship(pooled_output)
        
        return prediction_scores, seq_relationship_score


class BertOnlyMLMHead(nn.Module):
    """
    仅包含MLM的BERT头 - 用于只进行MLM预训练的情况
    """
    
    def __init__(
        self, 
        hidden_size: int, 
        vocab_size: int, 
        layer_norm_eps: float = 1e-12,
        hidden_act: str = "gelu"
    ):
        """
        初始化仅MLM头
        """
        super().__init__()
        self.predictions = BertLMPredictionHead(hidden_size, vocab_size, layer_norm_eps, hidden_act)
    
    def forward(self, sequence_output: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            sequence_output: 序列输出, shape: (batch_size, seq_len, hidden_size)
        
        Returns:
            prediction_scores: MLM预测分数, shape: (batch_size, seq_len, vocab_size)
        """
        prediction_scores = self.predictions(sequence_output)
        return prediction_scores


class BertOnlyNSPHead(nn.Module):
    """
    仅包含NSP的BERT头 - 用于只进行NSP预训练的情况
    """
    
    def __init__(self, hidden_size: int):
        """
        初始化仅NSP头
        """
        super().__init__()
        self.seq_relationship = nn.Linear(hidden_size, 2)
    
    def forward(self, pooled_output: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            pooled_output: 池化输出, shape: (batch_size, hidden_size)
        
        Returns:
            seq_relationship_score: NSP预测分数, shape: (batch_size, 2)
        """
        seq_relationship_score = self.seq_relationship(pooled_output)
        return seq_relationship_score


class BertClassificationHead(nn.Module):
    """
    BERT分类头 - 用于微调任务（如情感分析）
    """
    
    def __init__(
        self, 
        hidden_size: int, 
        num_labels: int, 
        dropout_prob: float = 0.1
    ):
        """
        初始化分类头
        
        Args:
            hidden_size: 隐藏层维度
            num_labels: 分类标签数量
            dropout_prob: dropout概率
        """
        super().__init__()
        
        self.dropout = nn.Dropout(dropout_prob)
        self.classifier = nn.Linear(hidden_size, num_labels)
    
    def forward(self, pooled_output: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            pooled_output: 池化输出, shape: (batch_size, hidden_size)
        
        Returns:
            logits: 分类logits, shape: (batch_size, num_labels)
        """
        pooled_output = self.dropout(pooled_output)
        logits = self.classifier(pooled_output)
        return logits
