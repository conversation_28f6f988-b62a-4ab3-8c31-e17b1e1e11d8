我准备使用Python的torch库从0实现bert框架，请你给我实现
    要求： 
    1:不用参考现有的项目文件，按你的理解从0实现,且可以新建文件,在deepai/bert目录下实现
    1：我的电脑是mac M1,使用gpu训练
    2：可使用使用hugginface 上的Salesforce/wikitext数据集下做预训练
    4：可以使用hugginface下载数据集，且可以使用huggingface的transformer库。
    7：实现完整的bert框架，我需要使用这份代码串联学习
    8：保存训练好的模型
    9: 可以使用pytorch中提供的方法，如nn.Embedding,nn.LayerNorm等,但是需要有bert框架的完整实现，供我系统学习
    10: 代码结构清晰，日志完善，注释完善
    11: 代码中涉及数据结构或超参数的，最好使用pydantic库用来指明数据结构,
    13: 写好训练和测试代码的入口函数，让我可以一键运行


这份bert的代码，有几个地方我还是不满意
1：可以参考transformer的代码，
2：bert中用到了transformer的Encoder，这份代码没有体现
3：bert的训练中会有两个任务：masked language model和next sentence prediction，这份代码需要体现
4：加上bert的做微调代码，使用noob123/imdb_review_3000 这个数据集做微调,这个数据集会输出两类，negative和positive
5: 自注意力，AddNorm等这些代码属于transformer架构的代码，可以统一整理，这样可以使bert的建构更清晰，更容易学习


还有几个问题需要改
1：参数或中间变量是tensor类型的，在注释里加上shape
2：代码中有README.md和ARCHITECTURE.md和GETTING_STARTED.md三个文档，请合并并精简
3: 请重新整理代码，合并功能重复的代码
4：