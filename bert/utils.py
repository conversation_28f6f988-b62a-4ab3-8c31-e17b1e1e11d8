"""
工具函数和日志配置
"""

import logging
import os
import json
import torch
import random
import numpy as np
from typing import Dict, Any, Optional
from pathlib import Path
import time
from datetime import datetime

from config import LoggingConfig


def setup_logging(config: LoggingConfig) -> logging.Logger:
    """设置日志系统"""
    
    # 创建日志目录
    if config.log_file:
        log_dir = Path(config.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 创建logger
    logger = logging.getLogger('BERT')
    logger.setLevel(getattr(logging, config.log_level.upper()))
    
    # 清除已有的handlers
    logger.handlers.clear()
    
    # 添加控制台handler
    if config.console_output:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 添加文件handler
    if config.log_file:
        file_handler = logging.FileHandler(config.log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def set_seed(seed: int = 42) -> None:
    """设置随机种子以确保可重现性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    # 对于MPS设备，确保确定性行为
    if torch.backends.mps.is_available():
        torch.mps.manual_seed(seed)


def save_config(config: Dict[str, Any], save_path: str) -> None:
    """保存配置到JSON文件"""
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 转换Pydantic模型为字典
    config_dict = {}
    for key, value in config.items():
        if hasattr(value, 'dict'):
            config_dict[key] = value.dict()
        else:
            config_dict[key] = value
    
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(config_dict, f, indent=2, ensure_ascii=False)


def load_config(config_path: str) -> Dict[str, Any]:
    """从JSON文件加载配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def count_parameters(model: torch.nn.Module) -> int:
    """计算模型参数数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


def format_time(seconds: float) -> str:
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.2f}s"
    elif seconds < 3600:
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{int(minutes)}m {seconds:.2f}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{int(hours)}h {int(minutes)}m {seconds:.2f}s"


def get_memory_usage() -> Dict[str, float]:
    """获取内存使用情况"""
    memory_info = {}
    
    # CPU内存
    import psutil
    process = psutil.Process()
    memory_info['cpu_memory_mb'] = process.memory_info().rss / 1024 / 1024
    
    # GPU内存
    if torch.cuda.is_available():
        memory_info['gpu_memory_mb'] = torch.cuda.memory_allocated() / 1024 / 1024
        memory_info['gpu_memory_cached_mb'] = torch.cuda.memory_reserved() / 1024 / 1024
    elif torch.backends.mps.is_available():
        # MPS内存信息（如果可用）
        try:
            memory_info['mps_memory_mb'] = torch.mps.current_allocated_memory() / 1024 / 1024
        except:
            memory_info['mps_memory_mb'] = 0
    
    return memory_info


class Timer:
    """计时器工具类"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """开始计时"""
        self.start_time = time.time()
        return self
    
    def stop(self):
        """停止计时"""
        self.end_time = time.time()
        return self
    
    def elapsed(self) -> float:
        """获取经过的时间（秒）"""
        if self.start_time is None:
            return 0.0
        end = self.end_time if self.end_time else time.time()
        return end - self.start_time
    
    def __enter__(self):
        return self.start()
    
    def __exit__(self, *args):
        self.stop()


class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self):
        self.metrics = {}
        self.history = {}
    
    def update(self, **kwargs):
        """更新指标"""
        for key, value in kwargs.items():
            if key not in self.history:
                self.history[key] = []
            self.history[key].append(value)
            self.metrics[key] = value
    
    def get_average(self, key: str, last_n: Optional[int] = None) -> float:
        """获取指标的平均值"""
        if key not in self.history:
            return 0.0
        
        values = self.history[key]
        if last_n:
            values = values[-last_n:]
        
        return sum(values) / len(values) if values else 0.0
    
    def reset(self):
        """重置所有指标"""
        self.metrics.clear()
        self.history.clear()
    
    def get_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        summary = {}
        for key in self.history:
            summary[key] = {
                'current': self.metrics.get(key, 0),
                'average': self.get_average(key),
                'count': len(self.history[key])
            }
        return summary


def create_output_dir(base_dir: str) -> str:
    """创建带时间戳的输出目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(base_dir, f"bert_run_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    return output_dir
