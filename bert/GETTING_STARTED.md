# BERT框架快速入门指南

## 🚀 5分钟快速开始

### 1. 环境准备

```bash
# 克隆或下载项目
cd bert

# 安装依赖
pip install -r requirements.txt
```

### 2. 验证环境

```bash
# 运行基础功能测试
python test_setup.py
```

如果看到所有测试都显示 ✓，说明环境配置正确。

### 3. 快速训练

```bash
# 运行快速训练（约1-2分钟）
python run.py quick
```

这将：
- 使用小规模BERT模型（256维，4层）
- 在Salesforce/wikitext-2数据集上训练
- 训练1个epoch，1000个样本
- 自动保存最佳模型

### 4. 测试模型

训练完成后，测试模型效果：

```bash
# 掩码语言模型测试
python run.py infer \
    --model_path ./bert_quick_test/bert_run_*/best_model \
    --mode mask \
    --text "The capital of France is [MASK]."

# 文本相似度测试
python run.py infer \
    --model_path ./bert_quick_test/bert_run_*/best_model \
    --mode similarity \
    --text "I love programming." \
    --text2 "I enjoy coding."
```

## 📚 进阶使用

### 完整训练

```bash
# 使用标准BERT配置训练
python run.py train \
    --hidden_size 768 \
    --num_hidden_layers 12 \
    --num_attention_heads 12 \
    --batch_size 16 \
    --num_epochs 3 \
    --dataset_config wikitext-103-raw-v1
```

### 自定义配置

1. 创建配置文件 `custom_config.json`：

```json
{
  "model": {
    "hidden_size": 512,
    "num_hidden_layers": 8,
    "num_attention_heads": 8
  },
  "training": {
    "batch_size": 32,
    "learning_rate": 1e-4,
    "num_epochs": 5
  }
}
```

2. 使用配置文件训练：

```bash
python main.py --config_file custom_config.json
```

## 🔧 常见问题

### Q: 训练速度慢怎么办？
A: 
- 减小batch_size
- 减少模型层数和隐藏维度
- 使用更少的训练样本（--max_samples参数）

### Q: 内存不足怎么办？
A:
- 减小batch_size到4或8
- 减小max_seq_length到64
- 使用CPU训练（--device cpu）

### Q: 如何使用自己的数据？
A: 修改data_loader.py中的load_wikitext_dataset函数，或者创建自己的数据加载函数。

### Q: 模型效果不好怎么办？
A:
- 增加训练轮数
- 使用更大的数据集
- 调整学习率
- 增加模型大小

## 📊 性能参考

| 配置 | 训练时间 | 内存使用 | 适用场景 |
|------|----------|----------|----------|
| 快速测试 | 1-2分钟 | 250MB | 功能验证 |
| 小型模型 | 10-20分钟 | 1GB | 学习实验 |
| 标准模型 | 1-2小时 | 4GB | 实际应用 |

## 🎯 学习建议

1. **从快速训练开始**：先运行`python run.py quick`了解整个流程
2. **阅读代码结构**：按照model.py → data_loader.py → trainer.py的顺序理解
3. **实验不同配置**：尝试修改模型大小、学习率等参数
4. **观察训练过程**：关注损失变化和内存使用情况
5. **测试模型效果**：使用不同的文本测试推理功能

## 📁 输出文件说明

训练完成后，输出目录包含：

```
bert_output/
├── config.json          # 模型配置
├── training_history.json # 训练历史
├── best_model/          # 最佳模型
│   ├── pytorch_model.bin
│   └── config.json
├── final_model/         # 最终模型
└── checkpoint-*/        # 训练检查点
```

## 🔗 相关资源

- [BERT原论文](https://arxiv.org/abs/1810.04805)
- [Transformer架构详解](https://arxiv.org/abs/1706.03762)
- [HuggingFace Transformers文档](https://huggingface.co/docs/transformers)

开始你的BERT学习之旅吧！🎉
