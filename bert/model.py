"""
BERT模型实现 - 重构版本
基于Transformer架构，包含完整的BERT实现：
1. 基础BERT模型
2. MLM预训练模型
3. NSP预训练模型
4. 完整预训练模型（MLM + NSP）
5. 分类微调模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple, Dict
import logging

from config import BertConfig
from bert_embeddings import BertEmbeddings, BertPooler
from bert_encoder import BertEncoder
from bert_heads import BertPreTrainingHeads, BertOnlyMLMHead, BertClassificationHead

logger = logging.getLogger('BERT')


class BertModel(nn.Module):
    """
    基础BERT模型 - 不包含任务特定的头
    这是BERT的核心，包含嵌入层和Transformer编码器
    """

    def __init__(self, config: BertConfig, add_pooling_layer: bool = True):
        """
        初始化BERT模型

        Args:
            config: BERT配置
            add_pooling_layer: 是否添加池化层
        """
        super().__init__()
        self.config = config

        # 嵌入层
        self.embeddings = BertEmbeddings(
            vocab_size=config.vocab_size,
            hidden_size=config.hidden_size,
            max_position_embeddings=config.max_position_embeddings,
            type_vocab_size=config.type_vocab_size,
            layer_norm_eps=config.layer_norm_eps,
            hidden_dropout_prob=config.hidden_dropout_prob
        )

        # Transformer编码器
        self.encoder = BertEncoder(
            hidden_size=config.hidden_size,
            num_hidden_layers=config.num_hidden_layers,
            num_attention_heads=config.num_attention_heads,
            intermediate_size=config.intermediate_size,
            hidden_dropout_prob=config.hidden_dropout_prob,
            attention_probs_dropout_prob=config.attention_probs_dropout_prob,
            layer_norm_eps=config.layer_norm_eps,
            hidden_act=config.hidden_act
        )

        # 池化层（可选）
        self.pooler = BertPooler(config.hidden_size) if add_pooling_layer else None

        # 初始化权重
        self.apply(self._init_weights)

        logger.info(f"BERT模型初始化完成，参数数量: {self.count_parameters():,}")

    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.Embedding):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.padding_idx is not None:
                module.weight.data[module.padding_idx].zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def count_parameters(self) -> int:
        """计算参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

    def get_input_embeddings(self):
        """获取输入嵌入层"""
        return self.embeddings.word_embeddings

    def set_input_embeddings(self, value):
        """设置输入嵌入层"""
        self.embeddings.word_embeddings = value

    def _create_attention_mask(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """创建注意力掩码"""
        if attention_mask is None:
            attention_mask = (input_ids != 0).float()

        # 扩展维度以适应多头注意力
        batch_size, seq_len = attention_mask.shape
        # 对于BERT，我们不需要因果掩码，所以直接使用padding掩码
        return attention_mask

    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        token_type_ids: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        inputs_embeds: Optional[torch.Tensor] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            input_ids: 输入token ids
            attention_mask: 注意力掩码
            token_type_ids: token类型ids
            position_ids: 位置ids
            head_mask: 头掩码
            inputs_embeds: 输入嵌入
            output_attentions: 是否输出注意力权重
            output_hidden_states: 是否输出所有隐藏状态
            return_dict: 是否返回字典格式

        Returns:
            包含模型输出的字典
        """
        output_attentions = output_attentions if output_attentions is not None else False
        output_hidden_states = output_hidden_states if output_hidden_states is not None else False
        return_dict = return_dict if return_dict is not None else True

        if input_ids is not None and inputs_embeds is not None:
            raise ValueError("不能同时指定input_ids和inputs_embeds")
        elif input_ids is not None:
            input_shape = input_ids.size()
        elif inputs_embeds is not None:
            input_shape = inputs_embeds.size()[:-1]
        else:
            raise ValueError("必须指定input_ids或inputs_embeds")

        batch_size, seq_length = input_shape
        device = input_ids.device if input_ids is not None else inputs_embeds.device

        # 创建注意力掩码
        if attention_mask is None:
            attention_mask = torch.ones(((batch_size, seq_length)), device=device)

        # 嵌入层
        embedding_output = self.embeddings(
            input_ids=input_ids,
            position_ids=position_ids,
            token_type_ids=token_type_ids,
            inputs_embeds=inputs_embeds,
        )

        # 编码器
        encoder_outputs = self.encoder(
            embedding_output,
            attention_mask=attention_mask,
            head_mask=head_mask,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )

        sequence_output = encoder_outputs['last_hidden_state']
        pooled_output = self.pooler(sequence_output) if self.pooler is not None else None

        if not return_dict:
            return (sequence_output, pooled_output) + encoder_outputs[1:]

        return {
            "last_hidden_state": sequence_output,
            "pooler_output": pooled_output,
            "hidden_states": encoder_outputs.get("hidden_states"),
            "attentions": encoder_outputs.get("attentions"),
        }


class BertForMaskedLM(nn.Module):
    """
    用于掩码语言模型的BERT - 只包含MLM任务
    """

    def __init__(self, config: BertConfig):
        """
        初始化MLM BERT模型

        Args:
            config: BERT配置
        """
        super().__init__()
        self.config = config

        # 基础BERT模型
        self.bert = BertModel(config, add_pooling_layer=False)

        # MLM头
        self.cls = BertOnlyMLMHead(
            hidden_size=config.hidden_size,
            vocab_size=config.vocab_size,
            layer_norm_eps=config.layer_norm_eps,
            hidden_act=config.hidden_act
        )

        # 权重共享：MLM头的输出层与输入嵌入层共享权重
        self.cls.predictions.decoder.weight = self.bert.embeddings.word_embeddings.weight

        # 初始化权重
        self.apply(self._init_weights)

        logger.info(f"BertForMaskedLM初始化完成，参数数量: {self.count_parameters():,}")

    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.Embedding):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.padding_idx is not None:
                module.weight.data[module.padding_idx].zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def count_parameters(self) -> int:
        """计算参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

    def get_input_embeddings(self):
        """获取输入嵌入层"""
        return self.bert.embeddings.word_embeddings

    def set_input_embeddings(self, value):
        """设置输入嵌入层"""
        self.bert.embeddings.word_embeddings = value
        self.cls.predictions.decoder.weight = value

    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        token_type_ids: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        inputs_embeds: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            input_ids: 输入token ids
            attention_mask: 注意力掩码
            token_type_ids: token类型ids
            position_ids: 位置ids
            head_mask: 头掩码
            inputs_embeds: 输入嵌入
            labels: MLM标签
            output_attentions: 是否输出注意力权重
            output_hidden_states: 是否输出所有隐藏状态
            return_dict: 是否返回字典格式

        Returns:
            包含损失和预测的字典
        """
        return_dict = return_dict if return_dict is not None else True

        # BERT前向传播
        outputs = self.bert(
            input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )

        sequence_output = outputs["last_hidden_state"]

        # MLM预测
        prediction_scores = self.cls(sequence_output)

        masked_lm_loss = None
        if labels is not None:
            loss_fct = nn.CrossEntropyLoss()  # -100 index = padding token
            masked_lm_loss = loss_fct(prediction_scores.view(-1, self.config.vocab_size), labels.view(-1))

        if not return_dict:
            output = (prediction_scores,) + outputs[2:]
            return ((masked_lm_loss,) + output) if masked_lm_loss is not None else output

        return {
            "loss": masked_lm_loss,
            "logits": prediction_scores,
            "hidden_states": outputs.get("hidden_states"),
            "attentions": outputs.get("attentions"),
        }


class BertForPreTraining(nn.Module):
    """
    用于预训练的BERT - 包含MLM和NSP两个任务
    """

    def __init__(self, config: BertConfig):
        """
        初始化预训练BERT模型

        Args:
            config: BERT配置
        """
        super().__init__()
        self.config = config

        # 基础BERT模型
        self.bert = BertModel(config)

        # 预训练头（包含MLM和NSP）
        self.cls = BertPreTrainingHeads(
            hidden_size=config.hidden_size,
            vocab_size=config.vocab_size,
            layer_norm_eps=config.layer_norm_eps,
            hidden_act=config.hidden_act
        )

        # 权重共享：MLM头的输出层与输入嵌入层共享权重
        self.cls.predictions.decoder.weight = self.bert.embeddings.word_embeddings.weight

        # 初始化权重
        self.apply(self._init_weights)

        logger.info(f"BertForPreTraining初始化完成，参数数量: {self.count_parameters():,}")

    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.Embedding):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.padding_idx is not None:
                module.weight.data[module.padding_idx].zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def count_parameters(self) -> int:
        """计算参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

    def get_input_embeddings(self):
        """获取输入嵌入层"""
        return self.bert.embeddings.word_embeddings

    def set_input_embeddings(self, value):
        """设置输入嵌入层"""
        self.bert.embeddings.word_embeddings = value
        self.cls.predictions.decoder.weight = value

    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        token_type_ids: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        inputs_embeds: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None,
        next_sentence_label: Optional[torch.Tensor] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            input_ids: 输入token ids
            attention_mask: 注意力掩码
            token_type_ids: token类型ids
            position_ids: 位置ids
            head_mask: 头掩码
            inputs_embeds: 输入嵌入
            labels: MLM标签
            next_sentence_label: NSP标签
            output_attentions: 是否输出注意力权重
            output_hidden_states: 是否输出所有隐藏状态
            return_dict: 是否返回字典格式

        Returns:
            包含损失和预测的字典
        """
        return_dict = return_dict if return_dict is not None else True

        # BERT前向传播
        outputs = self.bert(
            input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )

        sequence_output = outputs["last_hidden_state"]
        pooled_output = outputs["pooler_output"]

        # 预训练任务预测
        prediction_scores, seq_relationship_score = self.cls(sequence_output, pooled_output)

        total_loss = None
        if labels is not None and next_sentence_label is not None:
            loss_fct = nn.CrossEntropyLoss()

            # MLM损失
            masked_lm_loss = loss_fct(prediction_scores.view(-1, self.config.vocab_size), labels.view(-1))

            # NSP损失
            next_sentence_loss = loss_fct(seq_relationship_score.view(-1, 2), next_sentence_label.view(-1))

            total_loss = masked_lm_loss + next_sentence_loss

        if not return_dict:
            output = (prediction_scores, seq_relationship_score) + outputs[2:]
            return ((total_loss,) + output) if total_loss is not None else output

        return {
            "loss": total_loss,
            "prediction_logits": prediction_scores,
            "seq_relationship_logits": seq_relationship_score,
            "hidden_states": outputs.get("hidden_states"),
            "attentions": outputs.get("attentions"),
        }


class BertForSequenceClassification(nn.Module):
    """
    用于序列分类的BERT - 用于微调任务（如情感分析）
    """

    def __init__(self, config: BertConfig, num_labels: int = 2):
        """
        初始化分类BERT模型

        Args:
            config: BERT配置
            num_labels: 分类标签数量
        """
        super().__init__()
        self.config = config
        self.num_labels = num_labels

        # 基础BERT模型
        self.bert = BertModel(config)

        # 分类头
        self.dropout = nn.Dropout(config.hidden_dropout_prob)
        self.classifier = nn.Linear(config.hidden_size, num_labels)

        # 初始化权重
        self.apply(self._init_weights)

        logger.info(f"BertForSequenceClassification初始化完成，参数数量: {self.count_parameters():,}")

    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.Embedding):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.padding_idx is not None:
                module.weight.data[module.padding_idx].zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def count_parameters(self) -> int:
        """计算参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        token_type_ids: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        inputs_embeds: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            input_ids: 输入token ids
            attention_mask: 注意力掩码
            token_type_ids: token类型ids
            position_ids: 位置ids
            head_mask: 头掩码
            inputs_embeds: 输入嵌入
            labels: 分类标签
            output_attentions: 是否输出注意力权重
            output_hidden_states: 是否输出所有隐藏状态
            return_dict: 是否返回字典格式

        Returns:
            包含损失和预测的字典
        """
        return_dict = return_dict if return_dict is not None else True

        # BERT前向传播
        outputs = self.bert(
            input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )

        # 获取池化输出（[CLS] token的表示）
        pooled_output = outputs["pooler_output"]

        # 分类
        pooled_output = self.dropout(pooled_output)
        logits = self.classifier(pooled_output)

        loss = None
        if labels is not None:
            if self.config.problem_type is None:
                if self.num_labels == 1:
                    self.config.problem_type = "regression"
                elif self.num_labels > 1 and (labels.dtype == torch.long or labels.dtype == torch.int):
                    self.config.problem_type = "single_label_classification"
                else:
                    self.config.problem_type = "multi_label_classification"

            if self.config.problem_type == "regression":
                loss_fct = nn.MSELoss()
                if self.num_labels == 1:
                    loss = loss_fct(logits.squeeze(), labels.squeeze())
                else:
                    loss = loss_fct(logits, labels)
            elif self.config.problem_type == "single_label_classification":
                loss_fct = nn.CrossEntropyLoss()
                loss = loss_fct(logits.view(-1, self.num_labels), labels.view(-1))
            elif self.config.problem_type == "multi_label_classification":
                loss_fct = nn.BCEWithLogitsLoss()
                loss = loss_fct(logits, labels)

        if not return_dict:
            output = (logits,) + outputs[2:]
            return ((loss,) + output) if loss is not None else output

        return {
            "loss": loss,
            "logits": logits,
            "hidden_states": outputs.get("hidden_states"),
            "attentions": outputs.get("attentions"),
        }


# 导出所有模型类
__all__ = [
    "BertModel",
    "BertForMaskedLM",
    "BertForPreTraining",
    "BertForSequenceClassification"
]
