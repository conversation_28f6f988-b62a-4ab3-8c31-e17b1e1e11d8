#!/usr/bin/env python3
"""
测试BERT框架的基本功能
验证模型创建、数据加载等是否正常工作
"""

import torch
import logging
from transformers import AutoTokenizer

from config import BertConfig, TrainingConfig, DataConfig, LoggingConfig
from model import BertForMaskedLM
from data_loader import create_data_loader
from utils import setup_logging, set_seed

def test_config():
    """测试配置创建"""
    print("=== 测试配置创建 ===")

    # 创建配置
    bert_config = BertConfig(
        vocab_size=30522,
        hidden_size=256,  # 小一点用于测试
        num_hidden_layers=4,
        num_attention_heads=4
    )

    training_config = TrainingConfig(
        batch_size=4,
        num_epochs=1,
        max_seq_length=64
    )

    data_config = DataConfig(
        max_length=64
    )

    print(f"✓ BERT配置: {bert_config}")
    print(f"✓ 训练配置: {training_config}")
    print(f"✓ 数据配置: {data_config}")
    print()


def test_model_creation():
    """测试模型创建"""
    print("=== 测试模型创建 ===")

    # 设置日志
    logging_config = LoggingConfig(log_level="INFO")
    setup_logging(logging_config)

    # 创建小模型用于测试
    config = BertConfig(
        vocab_size=30522,
        hidden_size=256,
        num_hidden_layers=4,
        num_attention_heads=4,
        max_position_embeddings=128
    )

    # 创建模型
    model = BertForMaskedLM(config)

    print(f"✓ 模型创建成功")
    print(f"✓ 参数数量: {model.count_parameters():,}")

    # 测试前向传播
    batch_size = 2
    seq_len = 32

    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len))
    attention_mask = torch.ones(batch_size, seq_len)

    with torch.no_grad():
        outputs = model(input_ids=input_ids, attention_mask=attention_mask)

    print(f"✓ 前向传播成功")
    print(f"✓ 输出形状: {outputs['logits'].shape}")
    print()


def test_tokenizer():
    """测试tokenizer"""
    print("=== 测试Tokenizer ===")

    try:
        tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")

        # 确保有必要的特殊token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        if tokenizer.mask_token is None:
            tokenizer.add_special_tokens({'mask_token': '[MASK]'})

        # 测试tokenization
        text = "Hello, this is a test sentence."
        tokens = tokenizer(text, return_tensors="pt", padding=True, truncation=True, max_length=32)

        print(f"✓ Tokenizer加载成功")
        print(f"✓ 词汇表大小: {len(tokenizer)}")
        print(f"✓ 测试文本: {text}")
        print(f"✓ Token IDs形状: {tokens['input_ids'].shape}")
        print(f"✓ 解码测试: {tokenizer.decode(tokens['input_ids'][0])}")
        print()

        return tokenizer

    except Exception as e:
        print(f"✗ Tokenizer测试失败: {e}")
        print("这可能是网络问题，训练时会自动处理")
        print()
        return None


def test_data_loader():
    """测试数据加载器"""
    print("=== 测试数据加载器 ===")

    try:
        data_config = DataConfig(
            tokenizer_name="bert-base-uncased",
            max_length=64
        )

        training_config = TrainingConfig(
            batch_size=4,
            dataset_name="roneneldan/TinyStories-33M",
            num_workers=0  # 避免多进程问题
        )

        # 创建数据加载器（只加载少量样本用于测试）
        dataloader, tokenizer = create_data_loader(
            data_config,
            training_config,
            split="train",
            max_samples=10  # 只加载10个样本用于测试
        )

        print(f"✓ 数据加载器创建成功")
        print(f"✓ 批次数量: {len(dataloader)}")

        # 测试一个批次
        for batch in dataloader:
            print(f"✓ 批次形状: input_ids={batch['input_ids'].shape}, labels={batch['labels'].shape}")
            break

        print()
        return dataloader, tokenizer

    except Exception as e:
        print(f"✗ 数据加载器测试失败: {e}")
        print("这可能是网络问题，请检查网络连接")
        print()
        return None, None


def test_device():
    """测试设备检测"""
    print("=== 测试设备检测 ===")

    from config import get_device

    device = get_device()
    print(f"✓ 检测到设备: {device}")

    if device.type == "mps":
        print("✓ Mac M1/M2 MPS加速可用")
    elif device.type == "cuda":
        print("✓ NVIDIA GPU CUDA加速可用")
    else:
        print("✓ 使用CPU（建议使用GPU加速）")

    print()


def main():
    """主测试函数"""
    print("BERT框架基础功能测试")
    print("=" * 50)

    # 设置随机种子
    set_seed(42)

    # 运行各项测试
    test_config()
    test_device()
    test_model_creation()

    tokenizer = test_tokenizer()
    if tokenizer:
        dataloader, _ = test_data_loader()

    print("=" * 50)
    print("基础功能测试完成！")
    print()
    print("如果所有测试都通过，你可以运行:")
    print("  python run.py quick      # 快速训练测试")
    print("  python run.py train      # 完整训练")
    print()


if __name__ == "__main__":
    main()
