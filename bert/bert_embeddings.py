"""
BERT嵌入层实现
包含词嵌入、位置嵌入、类型嵌入和层归一化
"""

import torch
import torch.nn as nn
from typing import Optional
from transformer_components import LayerNorm


class BertEmbeddings(nn.Module):
    """
    BERT嵌入层：词嵌入 + 位置嵌入 + 类型嵌入 + LayerNorm + Dropout
    """
    
    def __init__(
        self, 
        vocab_size: int,
        hidden_size: int,
        max_position_embeddings: int = 512,
        type_vocab_size: int = 2,
        layer_norm_eps: float = 1e-12,
        hidden_dropout_prob: float = 0.1,
        pad_token_id: int = 0
    ):
        """
        初始化BERT嵌入层
        
        Args:
            vocab_size: 词汇表大小
            hidden_size: 隐藏层维度
            max_position_embeddings: 最大位置编码长度
            type_vocab_size: 句子类型词汇表大小（通常为2，用于区分句子A和B）
            layer_norm_eps: LayerNorm的epsilon值
            hidden_dropout_prob: dropout概率
            pad_token_id: padding token的id
        """
        super().__init__()
        
        self.vocab_size = vocab_size
        self.hidden_size = hidden_size
        self.max_position_embeddings = max_position_embeddings
        self.type_vocab_size = type_vocab_size
        self.pad_token_id = pad_token_id
        
        # 词嵌入层
        self.word_embeddings = nn.Embedding(
            vocab_size, 
            hidden_size, 
            padding_idx=pad_token_id
        )
        
        # 位置嵌入层
        self.position_embeddings = nn.Embedding(
            max_position_embeddings, 
            hidden_size
        )
        
        # 类型嵌入层（用于区分句子A和句子B）
        self.token_type_embeddings = nn.Embedding(
            type_vocab_size, 
            hidden_size
        )
        
        # LayerNorm和Dropout
        self.LayerNorm = LayerNorm(hidden_size, eps=layer_norm_eps)
        self.dropout = nn.Dropout(hidden_dropout_prob)
        # TODO 这两个ids我没有理解 
        # 注册位置id缓冲区（不需要梯度的参数）
        self.register_buffer(
            "position_ids", 
            torch.arange(max_position_embeddings).expand((1, -1)),
            persistent=False
        )
        
        # 注册token类型id缓冲区
        self.register_buffer(
            "token_type_ids",
            torch.zeros((1, max_position_embeddings), dtype=torch.long),
            persistent=False
        )
    
    def forward(
        self, 
        input_ids: torch.Tensor,
        token_type_ids: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        inputs_embeds: Optional[torch.Tensor] = None,
        past_key_values_length: int = 0
    ) -> torch.Tensor:
        """
        前向传播
        
        Args:
            input_ids: 输入token ids, shape: (batch_size, seq_len)
            token_type_ids: token类型ids, shape: (batch_size, seq_len)
            position_ids: 位置ids, shape: (batch_size, seq_len)
            inputs_embeds: 直接输入的嵌入向量（可选） TODO 这个是什么用处
            past_key_values_length: 过去键值对的长度（用于生成任务）
        
        Returns:
            embeddings: 嵌入向量, shape: (batch_size, seq_len, hidden_size)
        """
        if input_ids is not None:
            input_shape = input_ids.size()
        else:
            input_shape = inputs_embeds.size()[:-1]
        
        seq_length = input_shape[1]
        
        # 获取位置ids
        if position_ids is None:
            position_ids = self.position_ids[:, past_key_values_length : seq_length + past_key_values_length]
        
        # 获取token类型ids
        if token_type_ids is None:
            if hasattr(self, "token_type_ids"):
                buffered_token_type_ids = self.token_type_ids[:, :seq_length]
                buffered_token_type_ids_expanded = buffered_token_type_ids.expand(input_shape[0], seq_length)
                token_type_ids = buffered_token_type_ids_expanded
            else:
                token_type_ids = torch.zeros(input_shape, dtype=torch.long, device=self.position_ids.device)
        
        # 计算词嵌入
        if inputs_embeds is None:
            inputs_embeds = self.word_embeddings(input_ids)
        
        # 计算位置嵌入
        position_embeddings = self.position_embeddings(position_ids)
        
        # 计算类型嵌入
        token_type_embeddings = self.token_type_embeddings(token_type_ids)
        
        # 合并所有嵌入
        embeddings = inputs_embeds + position_embeddings + token_type_embeddings
        
        # LayerNorm和Dropout
        embeddings = self.LayerNorm(embeddings)
        embeddings = self.dropout(embeddings)
        
        return embeddings


class BertPooler(nn.Module):
    """
    BERT池化层 - 用于获取句子级别的表示
    通常用于分类任务，取[CLS] token的表示并通过一个线性层和激活函数
    """
    
    def __init__(self, hidden_size: int):
        """
        初始化池化层
        
        Args:
            hidden_size: 隐藏层维度
        """
        super().__init__()
        self.dense = nn.Linear(hidden_size, hidden_size)
        self.activation = nn.Tanh()
    
    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            hidden_states: 编码器输出, shape: (batch_size, seq_len, hidden_size)
        
        Returns:
            pooled_output: 池化输出, shape: (batch_size, hidden_size)
        """
        # 取第一个token（[CLS]）的隐藏状态
        first_token_tensor = hidden_states[:, 0]
        
        # 通过线性层和激活函数
        pooled_output = self.dense(first_token_tensor)
        pooled_output = self.activation(pooled_output)
        
        return pooled_output


def create_attention_mask_from_input_ids(input_ids: torch.Tensor, pad_token_id: int) -> torch.Tensor:
    """
    从输入ids创建注意力掩码
    
    Args:
        input_ids: 输入token ids, shape: (batch_size, seq_len)
        pad_token_id: padding token的id
    
    Returns:
        attention_mask: 注意力掩码, shape: (batch_size, seq_len)
        1表示真实token，0表示padding token
    """
    return (input_ids != pad_token_id).long()


def extend_attention_mask_for_decoder(attention_mask: torch.Tensor) -> torch.Tensor:
    """
    为解码器扩展注意力掩码，添加因果掩码
    
    Args:
        attention_mask: 基础注意力掩码, shape: (batch_size, seq_len)
    
    Returns:
        extended_mask: 扩展的注意力掩码, shape: (batch_size, 1, seq_len, seq_len)
    """
    batch_size, seq_len = attention_mask.shape
    
    # 创建因果掩码（下三角矩阵）
    causal_mask = torch.tril(torch.ones((seq_len, seq_len), device=attention_mask.device))
    
    # 扩展attention_mask维度
    extended_attention_mask = attention_mask.unsqueeze(1).unsqueeze(2)  # (batch_size, 1, 1, seq_len)
    
    # 应用因果掩码
    extended_attention_mask = extended_attention_mask * causal_mask.unsqueeze(0).unsqueeze(0)
    
    return extended_attention_mask


def get_extended_attention_mask(
    attention_mask: torch.Tensor, 
    input_shape: torch.Size, 
    device: torch.device,
    is_decoder: bool = False
) -> torch.Tensor:
    """
    获取扩展的注意力掩码
    
    Args:
        attention_mask: 原始注意力掩码
        input_shape: 输入形状
        device: 设备
        is_decoder: 是否为解码器
    
    Returns:
        扩展的注意力掩码
    """
    if attention_mask.dim() == 3:
        extended_attention_mask = attention_mask[:, None, :, :]
    elif attention_mask.dim() == 2:
        if is_decoder:
            batch_size, seq_length = input_shape
            seq_ids = torch.arange(seq_length, device=device)
            causal_mask = seq_ids[None, None, :].repeat(batch_size, seq_length, 1) <= seq_ids[None, :, None]
            causal_mask = causal_mask.to(attention_mask.dtype)
            
            if causal_mask.shape[1] < attention_mask.shape[1]:
                prefix_seq_len = attention_mask.shape[1] - causal_mask.shape[1]
                causal_mask = torch.cat(
                    [
                        torch.ones((batch_size, seq_length, prefix_seq_len), device=device, dtype=causal_mask.dtype),
                        causal_mask,
                    ],
                    axis=-1,
                )
            extended_attention_mask = causal_mask[:, None, :, :] * attention_mask[:, None, None, :]
        else:
            extended_attention_mask = attention_mask[:, None, None, :]
    else:
        raise ValueError(f"Wrong shape for input_ids (shape {input_shape}) or attention_mask (shape {attention_mask.shape})")
    
    # 转换为适合注意力计算的形式
    extended_attention_mask = extended_attention_mask.to(dtype=torch.float32)
    extended_attention_mask = (1.0 - extended_attention_mask) * torch.finfo(torch.float32).min
    
    return extended_attention_mask
