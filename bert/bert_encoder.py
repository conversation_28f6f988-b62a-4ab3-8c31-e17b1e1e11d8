"""
BERT编码器实现
基于Transformer编码器架构，使用多层TransformerEncoderLayer
"""

import torch
import torch.nn as nn
from typing import Optional, Tuple, List
from transformer_components import TransformerEncoderLayer


class BertEncoder(nn.Module):
    """
    BERT编码器 - 多层Transformer编码器的堆叠
    这是BERT的核心组件，体现了Transformer Encoder的架构
    """
    
    def __init__(
        self,
        hidden_size: int,
        num_hidden_layers: int,
        num_attention_heads: int,
        intermediate_size: int,
        hidden_dropout_prob: float = 0.1,
        attention_probs_dropout_prob: float = 0.1,
        layer_norm_eps: float = 1e-12,
        hidden_act: str = "gelu"
    ):
        """
        初始化BERT编码器
        
        Args:
            hidden_size: 隐藏层维度
            num_hidden_layers: Transformer层数
            num_attention_heads: 注意力头数
            intermediate_size: 前馈网络中间层维度
            hidden_dropout_prob: 隐藏层dropout概率
            attention_probs_dropout_prob: 注意力dropout概率
            layer_norm_eps: LayerNorm的epsilon值
            hidden_act: 激活函数类型
        """
        super().__init__()
        
        self.config = {
            'hidden_size': hidden_size,
            'num_hidden_layers': num_hidden_layers,
            'num_attention_heads': num_attention_heads,
            'intermediate_size': intermediate_size,
            'hidden_dropout_prob': hidden_dropout_prob,
            'attention_probs_dropout_prob': attention_probs_dropout_prob,
            'layer_norm_eps': layer_norm_eps,
            'hidden_act': hidden_act
        }
        
        # 创建多层Transformer编码器层
        self.layer = nn.ModuleList([
            TransformerEncoderLayer(
                d_model=hidden_size,
                n_heads=num_attention_heads,
                d_ff=intermediate_size,
                dropout=hidden_dropout_prob,
                activation=hidden_act
            )
            for _ in range(num_hidden_layers)
        ])
        
        self.gradient_checkpointing = False
    
    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        encoder_hidden_states: Optional[torch.Tensor] = None,
        encoder_attention_mask: Optional[torch.Tensor] = None,
        past_key_values: Optional[Tuple[Tuple[torch.Tensor]]] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = False,
        output_hidden_states: Optional[bool] = False,
        return_dict: Optional[bool] = True,
    ) -> Tuple[torch.Tensor, ...]:
        """
        前向传播
        
        Args:
            hidden_states: 输入隐藏状态, shape: (batch_size, seq_len, hidden_size)
            attention_mask: 注意力掩码, shape: (batch_size, seq_len)
            head_mask: 头掩码（用于屏蔽某些注意力头）
            encoder_hidden_states: 编码器隐藏状态（用于交叉注意力）
            encoder_attention_mask: 编码器注意力掩码
            past_key_values: 过去的键值对（用于生成任务）
            use_cache: 是否使用缓存
            output_attentions: 是否输出注意力权重
            output_hidden_states: 是否输出所有隐藏状态
            return_dict: 是否返回字典格式
        
        Returns:
            编码器输出
        """
        all_hidden_states = () if output_hidden_states else None
        all_self_attentions = () if output_attentions else None
        all_cross_attentions = () if output_attentions and self.config.get('add_cross_attention', False) else None
        
        if self.gradient_checkpointing and self.training:
            if use_cache:
                use_cache = False
        
        next_decoder_cache = () if use_cache else None
        
        for i, layer_module in enumerate(self.layer):
            if output_hidden_states:
                all_hidden_states = all_hidden_states + (hidden_states,)
            
            layer_head_mask = head_mask[i] if head_mask is not None else None
            past_key_value = past_key_values[i] if past_key_values is not None else None
            
            if self.gradient_checkpointing and self.training:
                # 使用梯度检查点来节省内存
                def create_custom_forward(module):
                    def custom_forward(*inputs):
                        return module(*inputs, output_attentions)
                    return custom_forward
                
                layer_outputs = torch.utils.checkpoint.checkpoint(
                    create_custom_forward(layer_module),
                    hidden_states,
                    attention_mask,
                )
            else:
                layer_outputs = layer_module(
                    hidden_states,
                    attention_mask=attention_mask,
                    output_attentions=output_attentions,
                )
            
            hidden_states = layer_outputs[0]
            
            if use_cache:
                next_decoder_cache += (layer_outputs[-1],)
            
            if output_attentions:
                all_self_attentions = all_self_attentions + (layer_outputs[1],)
        
        if output_hidden_states:
            all_hidden_states = all_hidden_states + (hidden_states,)
        
        if not return_dict:
            return tuple(
                v
                for v in [
                    hidden_states,
                    next_decoder_cache,
                    all_hidden_states,
                    all_self_attentions,
                    all_cross_attentions,
                ]
                if v is not None
            )
        
        return {
            'last_hidden_state': hidden_states,
            'past_key_values': next_decoder_cache,
            'hidden_states': all_hidden_states,
            'attentions': all_self_attentions,
            'cross_attentions': all_cross_attentions,
        }


class BertLayer(nn.Module):
    """
    单个BERT层 - 对TransformerEncoderLayer的封装
    提供更详细的BERT特定功能
    """
    
    def __init__(
        self,
        hidden_size: int,
        num_attention_heads: int,
        intermediate_size: int,
        hidden_dropout_prob: float = 0.1,
        attention_probs_dropout_prob: float = 0.1,
        layer_norm_eps: float = 1e-12,
        hidden_act: str = "gelu"
    ):
        """
        初始化BERT层
        """
        super().__init__()
        
        self.chunk_size_feed_forward = 0
        self.seq_len_dim = 1
        
        # 使用TransformerEncoderLayer作为基础
        self.transformer_layer = TransformerEncoderLayer(
            d_model=hidden_size,
            n_heads=num_attention_heads,
            d_ff=intermediate_size,
            dropout=hidden_dropout_prob,
            activation=hidden_act
        )
    
    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        encoder_hidden_states: Optional[torch.Tensor] = None,
        encoder_attention_mask: Optional[torch.Tensor] = None,
        past_key_value: Optional[Tuple[Tuple[torch.Tensor]]] = None,
        output_attentions: Optional[bool] = False,
    ) -> Tuple[torch.Tensor, ...]:
        """
        前向传播
        """
        # 对于BERT，我们只使用自注意力，不需要交叉注意力
        self_attn_past_key_value = past_key_value[:2] if past_key_value is not None else None
        
        # 使用TransformerEncoderLayer
        layer_output, attention_probs = self.transformer_layer(
            hidden_states,
            attention_mask=attention_mask,
            output_attentions=output_attentions
        )
        
        outputs = (layer_output,)
        
        if output_attentions:
            outputs = outputs + (attention_probs,)
        
        return outputs
    
    def feed_forward_chunk(self, attention_output):
        """
        前馈网络的分块处理（用于节省内存）
        """
        return self.transformer_layer.feed_forward(attention_output)


def apply_chunking_to_forward(
    forward_fn,
    chunk_size: int,
    chunk_dim: int,
    *input_tensors
):
    """
    将前向传播函数应用到分块的输入上
    用于节省内存的技术
    """
    assert len(input_tensors) > 0, "input_tensors必须是非空的"
    
    # 获取第一个张量的形状信息
    tensor_shape = input_tensors[0].shape[chunk_dim]
    
    # 如果张量足够小或者chunk_size为0，直接处理
    if chunk_size == 0 or tensor_shape < chunk_size:
        return forward_fn(*input_tensors)
    
    # 分块处理
    num_chunks = (tensor_shape + chunk_size - 1) // chunk_size
    output_chunks = []
    
    for i in range(num_chunks):
        start_idx = i * chunk_size
        end_idx = min((i + 1) * chunk_size, tensor_shape)
        
        # 获取当前块的输入
        chunk_inputs = []
        for tensor in input_tensors:
            if chunk_dim == 0:
                chunk_tensor = tensor[start_idx:end_idx]
            elif chunk_dim == 1:
                chunk_tensor = tensor[:, start_idx:end_idx]
            elif chunk_dim == 2:
                chunk_tensor = tensor[:, :, start_idx:end_idx]
            else:
                raise ValueError(f"不支持的chunk_dim: {chunk_dim}")
            chunk_inputs.append(chunk_tensor)
        
        # 处理当前块
        chunk_output = forward_fn(*chunk_inputs)
        output_chunks.append(chunk_output)
    
    # 合并输出
    return torch.cat(output_chunks, dim=chunk_dim)
