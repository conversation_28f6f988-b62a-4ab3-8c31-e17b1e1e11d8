"""
BERT微调模块
支持在预训练模型基础上进行分类任务微调
"""

import torch
import torch.nn as nn
from torch.optim import AdamW
from torch.optim.lr_scheduler import LinearLR
import os
import json
import logging
from typing import Dict, Any, Optional, Tuple, List
from tqdm import tqdm
import time
from pathlib import Path
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, classification_report

from config import BertConfig, TrainingConfig, get_device
from model import BertForSequenceClassification
from bert_data_loader import load_imdb_dataset, create_classification_dataloader
from utils import MetricsTracker, Timer, get_memory_usage, format_time

logger = logging.getLogger('BERT')


class BertFineTuner:
    """BERT微调器"""
    
    def __init__(
        self,
        model: BertForSequenceClassification,
        train_dataloader,
        val_dataloader,
        training_config: TrainingConfig,
        tokenizer=None
    ):
        self.model = model
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader
        self.config = training_config
        self.tokenizer = tokenizer
        
        # 设备配置
        self.device = self._setup_device()
        self.model.to(self.device)
        
        # 优化器和调度器
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        
        # 指标跟踪
        self.metrics_tracker = MetricsTracker()
        
        # 训练状态
        self.global_step = 0
        self.epoch = 0
        self.best_val_accuracy = 0.0
        
        # 输出目录
        self.output_dir = Path(training_config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"微调器初始化完成")
        logger.info(f"设备: {self.device}")
        logger.info(f"模型参数数量: {self.model.count_parameters():,}")
        logger.info(f"训练批次数量: {len(self.train_dataloader)}")
        if self.val_dataloader:
            logger.info(f"验证批次数量: {len(self.val_dataloader)}")
    
    def _setup_device(self) -> torch.device:
        """设置训练设备"""
        if self.config.device == "auto":
            device = get_device()
        else:
            device = torch.device(self.config.device)
        
        logger.info(f"使用设备: {device}")
        return device
    
    def _create_optimizer(self) -> torch.optim.Optimizer:
        """创建优化器"""
        # 分离权重衰减参数
        no_decay = ["bias", "LayerNorm.weight"]
        optimizer_grouped_parameters = [
            {
                "params": [p for n, p in self.model.named_parameters() 
                          if not any(nd in n for nd in no_decay)],
                "weight_decay": self.config.weight_decay,
            },
            {
                "params": [p for n, p in self.model.named_parameters() 
                          if any(nd in n for nd in no_decay)],
                "weight_decay": 0.0,
            },
        ]
        
        optimizer = AdamW(
            optimizer_grouped_parameters,
            lr=self.config.learning_rate,
            eps=self.config.adam_epsilon,
            betas=(self.config.adam_beta1, self.config.adam_beta2)
        )
        
        logger.info(f"创建AdamW优化器，学习率: {self.config.learning_rate}")
        return optimizer
    
    def _create_scheduler(self) -> torch.optim.lr_scheduler._LRScheduler:
        """创建学习率调度器"""
        total_steps = len(self.train_dataloader) * self.config.num_epochs
        
        # 线性衰减调度器
        scheduler = LinearLR(
            self.optimizer,
            start_factor=1.0,
            end_factor=0.0,
            total_iters=total_steps
        )
        
        logger.info(f"创建学习率调度器，总步数: {total_steps}")
        return scheduler
    
    def train_epoch(self) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        epoch_metrics = MetricsTracker()
        
        progress_bar = tqdm(
            self.train_dataloader,
            desc=f"微调 Epoch {self.epoch + 1}/{self.config.num_epochs}",
            leave=False
        )
        
        for step, batch in enumerate(progress_bar):
            # 移动数据到设备
            batch = {k: v.to(self.device) for k, v in batch.items()}
            
            # 前向传播
            outputs = self.model(**batch)
            loss = outputs["loss"]
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
            
            # 优化器步骤
            self.optimizer.step()
            self.scheduler.step()
            self.optimizer.zero_grad()
            
            # 更新指标
            epoch_metrics.update(
                loss=loss.item(),
                learning_rate=self.scheduler.get_last_lr()[0]
            )
            
            self.global_step += 1
            
            # 更新进度条
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'lr': f"{self.scheduler.get_last_lr()[0]:.2e}"
            })
            
            # 记录日志
            if self.global_step % self.config.logging_steps == 0:
                self._log_training_metrics(epoch_metrics)
        
        return epoch_metrics.get_summary()
    
    def evaluate(self) -> Dict[str, float]:
        """评估模型"""
        if not self.val_dataloader:
            return {}
        
        self.model.eval()
        val_metrics = MetricsTracker()
        
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for batch in tqdm(self.val_dataloader, desc="评估", leave=False):
                # 移动数据到设备
                batch = {k: v.to(self.device) for k, v in batch.items()}
                
                # 前向传播
                outputs = self.model(**batch)
                loss = outputs["loss"]
                logits = outputs["logits"]
                
                # 计算预测
                predictions = torch.argmax(logits, dim=-1)
                
                # 收集预测和标签
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(batch["labels"].cpu().numpy())
                
                # 更新指标
                val_metrics.update(val_loss=loss.item())
        
        # 计算分类指标
        accuracy = accuracy_score(all_labels, all_predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(
            all_labels, all_predictions, average='weighted'
        )
        
        val_metrics.update(
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1=f1
        )
        
        return val_metrics.get_summary()
    
    def fine_tune(self) -> Dict[str, Any]:
        """完整微调流程"""
        logger.info("开始微调")
        training_start_time = time.time()
        
        training_history = {
            "train_loss": [],
            "val_loss": [],
            "val_accuracy": [],
            "val_f1": [],
            "learning_rate": [],
            "epochs": []
        }
        
        for epoch in range(self.config.num_epochs):
            self.epoch = epoch
            epoch_start_time = time.time()
            
            # 训练一个epoch
            train_metrics = self.train_epoch()
            
            # 评估
            val_metrics = self.evaluate()
            
            # 记录历史
            training_history["train_loss"].append(train_metrics["loss"]["average"])
            training_history["learning_rate"].append(train_metrics["learning_rate"]["current"])
            training_history["epochs"].append(epoch + 1)
            
            if val_metrics:
                val_accuracy = val_metrics["accuracy"]["current"]
                val_f1 = val_metrics["f1"]["current"]
                val_loss = val_metrics["val_loss"]["average"]
                
                training_history["val_loss"].append(val_loss)
                training_history["val_accuracy"].append(val_accuracy)
                training_history["val_f1"].append(val_f1)
                
                # 保存最佳模型
                if val_accuracy > self.best_val_accuracy:
                    self.best_val_accuracy = val_accuracy
                    self._save_best_model()
            
            # 记录epoch总结
            epoch_time = time.time() - epoch_start_time
            logger.info(f"Epoch {epoch + 1} 完成")
            logger.info(f"训练损失: {train_metrics['loss']['average']:.4f}")
            if val_metrics:
                logger.info(f"验证损失: {val_metrics['val_loss']['average']:.4f}")
                logger.info(f"验证准确率: {val_metrics['accuracy']['current']:.4f}")
                logger.info(f"验证F1: {val_metrics['f1']['current']:.4f}")
            logger.info(f"学习率: {train_metrics['learning_rate']['current']:.2e}")
            logger.info(f"用时: {format_time(epoch_time)}")
        
        # 微调完成
        total_time = time.time() - training_start_time
        logger.info(f"微调完成，总用时: {format_time(total_time)}")
        logger.info(f"最佳验证准确率: {self.best_val_accuracy:.4f}")
        
        # 保存最终模型
        self._save_final_model()
        
        # 保存训练历史
        self._save_training_history(training_history)
        
        return training_history
    
    def _log_training_metrics(self, metrics: MetricsTracker):
        """记录训练指标"""
        memory_info = get_memory_usage()
        logger.info(
            f"Step {self.global_step}: "
            f"loss={metrics.metrics.get('loss', 0):.4f}, "
            f"lr={metrics.metrics.get('learning_rate', 0):.2e}, "
            f"memory={memory_info.get('cpu_memory_mb', 0):.1f}MB"
        )
    
    def _save_best_model(self):
        """保存最佳模型"""
        best_model_dir = self.output_dir / "best_model"
        best_model_dir.mkdir(exist_ok=True)
        
        torch.save(self.model.state_dict(), best_model_dir / "pytorch_model.bin")
        
        with open(best_model_dir / "config.json", 'w') as f:
            json.dump(self.model.config.dict(), f, indent=2)
        
        logger.info(f"保存最佳模型，验证准确率: {self.best_val_accuracy:.4f}")
    
    def _save_final_model(self):
        """保存最终模型"""
        final_model_dir = self.output_dir / "final_model"
        final_model_dir.mkdir(exist_ok=True)
        
        torch.save(self.model.state_dict(), final_model_dir / "pytorch_model.bin")
        
        with open(final_model_dir / "config.json", 'w') as f:
            json.dump(self.model.config.dict(), f, indent=2)
        
        logger.info("保存最终模型")
    
    def _save_training_history(self, history: Dict[str, Any]):
        """保存训练历史"""
        with open(self.output_dir / "fine_tuning_history.json", 'w') as f:
            json.dump(history, f, indent=2)
        
        logger.info("保存微调历史")


def fine_tune_on_imdb(
    pretrained_model_path: str,
    output_dir: str = "./bert_fine_tuned",
    num_epochs: int = 3,
    batch_size: int = 16,
    learning_rate: float = 2e-5,
    max_samples: Optional[int] = None
) -> Dict[str, Any]:
    """
    在IMDB数据集上微调BERT
    
    Args:
        pretrained_model_path: 预训练模型路径
        output_dir: 输出目录
        num_epochs: 训练轮数
        batch_size: 批次大小
        learning_rate: 学习率
        max_samples: 最大样本数量
    
    Returns:
        训练历史
    """
    # 加载预训练模型配置
    config_path = os.path.join(pretrained_model_path, "config.json")
    with open(config_path, 'r') as f:
        config_dict = json.load(f)
    
    config = BertConfig(**config_dict)
    
    # 创建分类模型
    model = BertForSequenceClassification(config, num_labels=2)
    
    # 加载预训练权重
    pretrained_weights = torch.load(
        os.path.join(pretrained_model_path, "pytorch_model.bin"),
        map_location="cpu"
    )
    
    # 只加载BERT部分的权重
    bert_weights = {}
    for key, value in pretrained_weights.items():
        if key.startswith("bert."):
            bert_weights[key] = value
    
    model.load_state_dict(bert_weights, strict=False)
    logger.info("成功加载预训练权重")
    
    # 加载数据
    from transformers import AutoTokenizer
    tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
    
    train_texts, train_labels = load_imdb_dataset("noob123/imdb_review_3000", "train", max_samples)
    
    # 分割训练和验证集
    split_idx = int(0.8 * len(train_texts))
    val_texts = train_texts[split_idx:]
    val_labels = train_labels[split_idx:]
    train_texts = train_texts[:split_idx]
    train_labels = train_labels[:split_idx]
    
    # 创建数据加载器
    train_dataloader = create_classification_dataloader(
        train_texts, train_labels, tokenizer, batch_size, shuffle=True
    )
    val_dataloader = create_classification_dataloader(
        val_texts, val_labels, tokenizer, batch_size, shuffle=False
    )
    
    # 创建训练配置
    training_config = TrainingConfig(
        batch_size=batch_size,
        learning_rate=learning_rate,
        num_epochs=num_epochs,
        output_dir=output_dir
    )
    
    # 创建微调器
    fine_tuner = BertFineTuner(
        model=model,
        train_dataloader=train_dataloader,
        val_dataloader=val_dataloader,
        training_config=training_config,
        tokenizer=tokenizer
    )
    
    # 开始微调
    return fine_tuner.fine_tune()
