{"model": {"vocab_size": 30522, "hidden_size": 256, "num_hidden_layers": 4, "num_attention_heads": 4, "intermediate_size": 3072, "max_position_embeddings": 512, "type_vocab_size": 2, "hidden_dropout_prob": 0.1, "attention_probs_dropout_prob": 0.1, "layer_norm_eps": 1e-12, "hidden_act": "gelu", "initializer_range": 0.02}, "training": {"batch_size": 8, "learning_rate": 0.0001, "num_epochs": 1, "warmup_steps": 1000, "max_grad_norm": 1.0, "weight_decay": 0.01, "adam_epsilon": 1e-08, "adam_beta1": 0.9, "adam_beta2": 0.999, "max_seq_length": 64, "mlm_probability": 0.15, "device": "auto", "num_workers": 4, "save_steps": 1000, "logging_steps": 100, "output_dir": "./bert_quick_test/bert_run_20250527_220428", "save_total_limit": 3, "dataset_name": "roneneldan/TinyStories-33M", "dataset_config": null, "train_split": "train", "validation_split": "validation"}, "data": {"tokenizer_name": "bert-base-uncased", "do_lower_case": true, "max_length": 64, "padding": "max_length", "truncation": true, "mlm_probability": 0.15, "mask_token": "[MASK]"}, "logging": {"log_level": "INFO", "log_file": "./bert_quick_test/bert_run_20250527_220428/training.log", "console_output": true}}