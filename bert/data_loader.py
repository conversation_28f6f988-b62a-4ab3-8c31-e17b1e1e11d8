"""
数据加载和预处理模块
支持HuggingFace数据集和MLM任务的数据处理
"""

import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
from datasets import load_dataset
import random
import logging
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
from tqdm import tqdm

from config import DataConfig, TrainingConfig

logger = logging.getLogger('BERT')


class MLMDataCollator:
    """MLM数据整理器，负责动态掩码"""

    def __init__(self, tokenizer, mlm_probability: float = 0.15):
        self.tokenizer = tokenizer
        self.mlm_probability = mlm_probability

    def __call__(self, examples: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
        """
        整理批次数据并应用MLM掩码

        Args:
            examples: 批次样本列表

        Returns:
            整理后的批次数据
        """
        batch = self.tokenizer.pad(examples, return_tensors="pt")

        # 应用MLM掩码
        batch["input_ids"], batch["labels"] = self.mask_tokens(
            batch["input_ids"],
            special_tokens_mask=batch.get("special_tokens_mask")
        )

        return batch

    def mask_tokens(
        self,
        inputs: torch.Tensor,
        special_tokens_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        对输入应用MLM掩码

        Args:
            inputs: 输入token ids
            special_tokens_mask: 特殊token掩码

        Returns:
            masked_inputs: 掩码后的输入
            labels: MLM标签
        """
        labels = inputs.clone()

        # 创建概率掩码
        probability_matrix = torch.full(labels.shape, self.mlm_probability)

        if special_tokens_mask is not None:
            probability_matrix.masked_fill_(special_tokens_mask.bool(), value=0.0)

        # 不掩码padding tokens
        if self.tokenizer.pad_token_id is not None:
            padding_mask = labels.eq(self.tokenizer.pad_token_id)
            probability_matrix.masked_fill_(padding_mask, value=0.0)

        masked_indices = torch.bernoulli(probability_matrix).bool()
        labels[~masked_indices] = -100  # 只计算被掩码token的损失

        # 80%的时间用[MASK]替换
        indices_replaced = torch.bernoulli(torch.full(labels.shape, 0.8)).bool() & masked_indices
        inputs[indices_replaced] = self.tokenizer.mask_token_id

        # 10%的时间用随机token替换
        indices_random = torch.bernoulli(torch.full(labels.shape, 0.5)).bool() & masked_indices & ~indices_replaced
        random_words = torch.randint(len(self.tokenizer), labels.shape, dtype=torch.long)
        inputs[indices_random] = random_words[indices_random]

        # 剩余10%保持不变

        return inputs, labels


class TextDataset(Dataset):
    """文本数据集类"""

    def __init__(
        self,
        texts: List[str],
        tokenizer,
        max_length: int = 128,
        return_special_tokens_mask: bool = True
    ):
        self.texts = texts
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.return_special_tokens_mask = return_special_tokens_mask

        logger.info(f"创建文本数据集，样本数量: {len(texts)}")

    def __len__(self) -> int:
        return len(self.texts)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """获取单个样本"""
        text = self.texts[idx]

        # tokenize文本
        encoding = self.tokenizer(
            text,
            truncation=True,
            max_length=self.max_length,
            padding=False,  # 在collator中进行padding
            return_tensors="pt",
            return_special_tokens_mask=self.return_special_tokens_mask
        )

        # 移除batch维度
        item = {key: val.squeeze(0) for key, val in encoding.items()}

        return item


def load_tinystories_dataset(
    dataset_name: str = "roneneldan/TinyStories-33M",
    split: str = "train",
    max_samples: Optional[int] = None,
    streaming: bool = False
) -> List[str]:
    """
    加载TinyStories数据集

    Args:
        dataset_name: 数据集名称
        split: 数据集分割
        max_samples: 最大样本数量
        streaming: 是否使用流式加载

    Returns:
        文本列表
    """
    logger.info(f"加载数据集: {dataset_name}, split: {split}")

    try:
        # 加载数据集
        dataset = load_dataset(dataset_name, split=split, streaming=streaming)

        texts = []
        if streaming:
            # 流式处理
            for i, example in enumerate(tqdm(dataset, desc="加载数据")):
                if max_samples and i >= max_samples:
                    break
                texts.append(example['text'])
        else:
            # 一次性加载
            if max_samples:
                dataset = dataset.select(range(min(max_samples, len(dataset))))
            texts = dataset['text']

        logger.info(f"成功加载 {len(texts)} 个文本样本")
        return texts

    except Exception as e:
        logger.error(f"加载数据集失败: {e}")
        # 返回一些示例数据作为fallback
        logger.warning("使用示例数据作为fallback")
        return [
            "Once upon a time, there was a little girl named Lucy.",
            "She loved to play in the garden with her toys.",
            "One day, she found a magical flower that could talk.",
            "The flower told her amazing stories about faraway lands.",
            "Lucy listened carefully and learned many new things."
        ] * 100  # 重复以提供足够的训练数据


def create_data_loader(
    data_config: DataConfig,
    training_config: TrainingConfig,
    split: str = "train",
    max_samples: Optional[int] = None
) -> DataLoader:
    """
    创建数据加载器

    Args:
        data_config: 数据配置
        training_config: 训练配置
        split: 数据集分割
        max_samples: 最大样本数量

    Returns:
        数据加载器
    """
    # 加载tokenizer
    logger.info(f"加载tokenizer: {data_config.tokenizer_name}")
    tokenizer = AutoTokenizer.from_pretrained(
        data_config.tokenizer_name,
        do_lower_case=data_config.do_lower_case
    )

    # 确保有必要的特殊token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    if tokenizer.mask_token is None:
        tokenizer.add_special_tokens({'mask_token': '[MASK]'})

    # 加载数据
    texts = load_tinystories_dataset(
        dataset_name=training_config.dataset_name,
        split=split,
        max_samples=max_samples
    )

    # 创建数据集
    dataset = TextDataset(
        texts=texts,
        tokenizer=tokenizer,
        max_length=data_config.max_length,
        return_special_tokens_mask=True
    )

    # 创建数据整理器
    data_collator = MLMDataCollator(
        tokenizer=tokenizer,
        mlm_probability=data_config.mlm_probability
    )

    # 创建数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=training_config.batch_size,
        shuffle=(split == "train"),
        num_workers=training_config.num_workers,
        collate_fn=data_collator,
        pin_memory=True
    )

    logger.info(f"创建数据加载器完成，批次数量: {len(dataloader)}")

    return dataloader, tokenizer


def get_vocab_size_from_tokenizer(tokenizer_name: str) -> int:
    """获取tokenizer的词汇表大小"""
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
    return len(tokenizer)
