"""
Transformer基础组件
包含自注意力、AddNorm、前馈网络等核心组件
参考transformer目录的实现，为BERT提供基础架构
"""

import torch
import torch.nn as nn
import math
from typing import Optional, Tuple
import torch.nn.functional as F


class MultiHeadSelfAttention(nn.Module):
    """
    多头自注意力机制 - 专门用于BERT的自注意力
    """

    def __init__(self, d_model: int, n_heads: int, dropout: float = 0.1):
        """
        初始化多头自注意力

        Args:
            d_model: 模型维度
            n_heads: 注意力头数
            dropout: dropout概率
        """
        super().__init__()

        assert d_model % n_heads == 0, "d_model必须能被n_heads整除"

        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads  # 每个头的维度

        # 线性变换层 - BERT中Q、K、V都来自同一个输入
        self.w_q = nn.Linear(d_model, d_model, bias=True)
        self.w_k = nn.Linear(d_model, d_model, bias=True)
        self.w_v = nn.Linear(d_model, d_model, bias=True)
        self.w_o = nn.Linear(d_model, d_model, bias=True)

        self.dropout = nn.Dropout(dropout)

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """初始化权重"""
        for module in [self.w_q, self.w_k, self.w_v, self.w_o]:
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.zeros_(module.bias)

    def scaled_dot_product_attention(
        self,
        q: torch.Tensor,
        k: torch.Tensor,
        v: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        缩放点积注意力

        Args:
            q: 查询张量 [batch_size, n_heads, seq_len, d_k]
            k: 键张量 [batch_size, n_heads, seq_len, d_k]
            v: 值张量 [batch_size, n_heads, seq_len, d_k]
            mask: 注意力掩码

        Returns:
            (注意力输出, 注意力权重)
        """
        d_k = q.size(-1)

        # 计算注意力分数: Q * K^T / sqrt(d_k)
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(d_k)

        # 应用掩码
        if mask is not None:
            # 将掩码位置设为很小的负数
            scores = scores.masked_fill(mask == 0, -1e9)

        # 计算注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        # 计算注意力输出
        output = torch.matmul(attention_weights, v)

        return output, attention_weights

    def forward(
        self,
        x: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        output_attentions: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播

        Args:
            x: 输入张量 [batch_size, seq_len, d_model]
            attention_mask: 注意力掩码 [batch_size, seq_len]
            output_attentions: 是否输出注意力权重

        Returns:
            (注意力输出, 注意力权重)
        """
        batch_size, seq_len, _ = x.shape

        # 线性变换 - BERT中Q、K、V都来自同一个输入x
        Q = self.w_q(x)  # [batch_size, seq_len, d_model]
        K = self.w_k(x)  # [batch_size, seq_len, d_model]
        V = self.w_v(x)  # [batch_size, seq_len, d_model]

        # 重塑为多头形式: [batch_size, seq_len, n_heads, d_k] -> [batch_size, n_heads, seq_len, d_k]
        Q = Q.view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = K.view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        V = V.view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)

        # 处理注意力掩码
        if attention_mask is not None:
            # 扩展掩码维度以适应多头注意力
            # [batch_size, seq_len] -> [batch_size, 1, 1, seq_len]
            attention_mask = attention_mask.unsqueeze(1).unsqueeze(2)
            # 扩展到所有头
            attention_mask = attention_mask.expand(batch_size, self.n_heads, seq_len, seq_len)

        # 计算注意力
        attention_output, attention_probs = self.scaled_dot_product_attention(Q, K, V, attention_mask)
        # attention_output: [batch_size, n_heads, seq_len, d_k]

        # 重塑回原始形状: [batch_size, n_heads, seq_len, d_k] -> [batch_size, seq_len, d_model]
        attention_output = (
            attention_output.transpose(1, 2)
            .contiguous()
            .view(batch_size, seq_len, self.d_model)
        )

        # 最终线性变换
        output = self.w_o(attention_output)

        if output_attentions:
            return output, attention_probs
        else:
            return output, None


class FeedForward(nn.Module):
    """
    前馈网络 - BERT中的Position-wise Feed-Forward Network
    """

    def __init__(self, d_model: int, d_ff: int, dropout: float = 0.1, activation: str = "gelu"):
        """
        初始化前馈网络

        Args:
            d_model: 模型维度
            d_ff: 前馈网络隐藏层维度
            dropout: dropout概率
            activation: 激活函数类型
        """
        super().__init__()

        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        
        # 激活函数
        if activation == "gelu":
            self.activation = F.gelu
        elif activation == "relu":
            self.activation = F.relu
        else:
            raise ValueError(f"不支持的激活函数: {activation}")

        # 初始化权重
        nn.init.xavier_uniform_(self.linear1.weight)
        nn.init.xavier_uniform_(self.linear2.weight)
        nn.init.zeros_(self.linear1.bias)
        nn.init.zeros_(self.linear2.bias)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量 [batch_size, seq_len, d_model]

        Returns:
            前馈网络输出
        """
        # FFN(x) = activation(xW1 + b1)W2 + b2
        return self.linear2(self.dropout(self.activation(self.linear1(x))))


class LayerNorm(nn.Module):
    """
    层归一化 - 自定义实现以便更好地理解
    """

    def __init__(self, d_model: int, eps: float = 1e-12):
        """
        初始化层归一化

        Args:
            d_model: 模型维度
            eps: 数值稳定性参数
        """
        super().__init__()
        self.gamma = nn.Parameter(torch.ones(d_model))  # 缩放参数
        self.beta = nn.Parameter(torch.zeros(d_model))  # 偏移参数
        self.eps = eps

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量 [batch_size, seq_len, d_model]

        Returns:
            归一化后的张量
        """
        # 计算均值和方差（在最后一个维度上）
        mean = x.mean(dim=-1, keepdim=True)
        var = x.var(dim=-1, keepdim=True, unbiased=False)

        # 归一化
        normalized = (x - mean) / torch.sqrt(var + self.eps)

        # 缩放和平移
        return self.gamma * normalized + self.beta


class AddNorm(nn.Module):
    """
    残差连接 + 层归一化 (Add & Norm)
    实现 Transformer 中的 AddNorm 模块
    """

    def __init__(self, d_model: int, dropout: float = 0.1, eps: float = 1e-12):
        """
        初始化 AddNorm 模块

        Args:
            d_model: 模型维度
            dropout: dropout概率
            eps: 层归一化的数值稳定性参数
        """
        super().__init__()

        self.layer_norm = LayerNorm(d_model, eps)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, sublayer_output: torch.Tensor) -> torch.Tensor:
        """
        前向传播：残差连接 + 层归一化

        Args:
            x: 原始输入张量 [batch_size, seq_len, d_model]
            sublayer_output: 子层输出张量 [batch_size, seq_len, d_model]

        Returns:
            AddNorm 输出 [batch_size, seq_len, d_model]
        """
        # 残差连接 + dropout + 层归一化
        # 使用 Post-LN 方式: LayerNorm(x + Sublayer(x))
        return self.layer_norm(x + self.dropout(sublayer_output))


class TransformerEncoderLayer(nn.Module):
    """
    Transformer编码器层 - BERT的基础构建块
    """

    def __init__(self, d_model: int, n_heads: int, d_ff: int, dropout: float = 0.1, activation: str = "gelu"):
        """
        初始化编码器层

        Args:
            d_model: 模型维度
            n_heads: 注意力头数
            d_ff: 前馈网络维度
            dropout: dropout概率
            activation: 激活函数类型
        """
        super().__init__()

        self.self_attention = MultiHeadSelfAttention(d_model, n_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout, activation)

        # 使用 AddNorm 模块
        self.add_norm1 = AddNorm(d_model, dropout)
        self.add_norm2 = AddNorm(d_model, dropout)

    def forward(
        self, 
        x: torch.Tensor, 
        attention_mask: Optional[torch.Tensor] = None,
        output_attentions: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播

        Args:
            x: 输入张量 [batch_size, seq_len, d_model]
            attention_mask: 注意力掩码
            output_attentions: 是否输出注意力权重

        Returns:
            (编码器层输出, 注意力权重)
        """
        # 自注意力 + AddNorm
        attn_output, attention_probs = self.self_attention(x, attention_mask, output_attentions)
        x = self.add_norm1(x, attn_output)

        # 前馈网络 + AddNorm
        ff_output = self.feed_forward(x)
        x = self.add_norm2(x, ff_output)

        return x, attention_probs
