#!/usr/bin/env python3
"""
BERT训练和推理的便捷运行脚本
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path


def run_training(args):
    """运行训练"""
    cmd = [
        sys.executable, "main.py",
        "--output_dir", args.output_dir,
        "--batch_size", str(args.batch_size),
        "--learning_rate", str(args.learning_rate),
        "--num_epochs", str(args.num_epochs),
        "--max_seq_length", str(args.max_seq_length),
        "--hidden_size", str(args.hidden_size),
        "--num_hidden_layers", str(args.num_hidden_layers),
        "--num_attention_heads", str(args.num_attention_heads),
        "--device", args.device,
        "--seed", str(args.seed)
    ]
    
    if args.max_samples:
        cmd.extend(["--max_samples", str(args.max_samples)])
    
    if args.config_file:
        cmd.extend(["--config_file", args.config_file])
    
    print(f"运行命令: {' '.join(cmd)}")
    subprocess.run(cmd)


def run_inference(args):
    """运行推理"""
    cmd = [
        sys.executable, "inference.py",
        "--model_path", args.model_path,
        "--mode", args.mode
    ]
    
    if args.text:
        cmd.extend(["--text", args.text])
    
    if args.text2:
        cmd.extend(["--text2", args.text2])
    
    if args.tokenizer_name:
        cmd.extend(["--tokenizer_name", args.tokenizer_name])
    
    print(f"运行命令: {' '.join(cmd)}")
    subprocess.run(cmd)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BERT训练和推理便捷脚本")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 训练命令
    train_parser = subparsers.add_parser("train", help="训练BERT模型")
    train_parser.add_argument("--output_dir", type=str, default="./bert_output",
                             help="输出目录")
    train_parser.add_argument("--config_file", type=str, default=None,
                             help="配置文件路径")
    train_parser.add_argument("--batch_size", type=int, default=16,
                             help="批次大小")
    train_parser.add_argument("--learning_rate", type=float, default=5e-5,
                             help="学习率")
    train_parser.add_argument("--num_epochs", type=int, default=3,
                             help="训练轮数")
    train_parser.add_argument("--max_seq_length", type=int, default=128,
                             help="最大序列长度")
    train_parser.add_argument("--hidden_size", type=int, default=768,
                             help="隐藏层维度")
    train_parser.add_argument("--num_hidden_layers", type=int, default=12,
                             help="Transformer层数")
    train_parser.add_argument("--num_attention_heads", type=int, default=12,
                             help="注意力头数")
    train_parser.add_argument("--device", type=str, default="auto",
                             help="训练设备")
    train_parser.add_argument("--seed", type=int, default=42,
                             help="随机种子")
    train_parser.add_argument("--max_samples", type=int, default=None,
                             help="最大样本数量（用于测试）")
    
    # 推理命令
    infer_parser = subparsers.add_parser("infer", help="运行BERT推理")
    infer_parser.add_argument("--model_path", type=str, required=True,
                             help="模型路径")
    infer_parser.add_argument("--mode", type=str, default="mask",
                             choices=["mask", "similarity", "embedding"],
                             help="推理模式")
    infer_parser.add_argument("--text", type=str, default=None,
                             help="要处理的文本")
    infer_parser.add_argument("--text2", type=str, default=None,
                             help="第二个文本（用于相似度计算）")
    infer_parser.add_argument("--tokenizer_name", type=str, default="bert-base-uncased",
                             help="tokenizer名称")
    
    # 快速开始命令
    quick_parser = subparsers.add_parser("quick", help="快速开始（小规模训练）")
    quick_parser.add_argument("--output_dir", type=str, default="./bert_quick_test",
                             help="输出目录")
    
    # 示例命令
    example_parser = subparsers.add_parser("example", help="运行示例")
    
    args = parser.parse_args()
    
    if args.command == "train":
        run_training(args)
    elif args.command == "infer":
        run_inference(args)
    elif args.command == "quick":
        # 快速训练配置
        args.batch_size = 8
        args.learning_rate = 1e-4
        args.num_epochs = 1
        args.max_seq_length = 64
        args.hidden_size = 256
        args.num_hidden_layers = 4
        args.num_attention_heads = 4
        args.device = "auto"
        args.seed = 42
        args.max_samples = 1000
        args.config_file = None
        
        print("=== 快速训练模式 ===")
        print("使用小规模配置进行快速测试")
        print(f"批次大小: {args.batch_size}")
        print(f"学习率: {args.learning_rate}")
        print(f"训练轮数: {args.num_epochs}")
        print(f"隐藏层维度: {args.hidden_size}")
        print(f"Transformer层数: {args.num_hidden_layers}")
        print(f"最大样本数: {args.max_samples}")
        print()
        
        run_training(args)
    elif args.command == "example":
        print("=== BERT框架使用示例 ===")
        print()
        print("1. 快速训练（小规模测试）:")
        print("   python run.py quick")
        print()
        print("2. 完整训练:")
        print("   python run.py train --num_epochs 3 --batch_size 16")
        print()
        print("3. 自定义配置训练:")
        print("   python run.py train --hidden_size 512 --num_hidden_layers 8 --batch_size 32")
        print()
        print("4. 掩码语言模型推理:")
        print("   python run.py infer --model_path ./bert_output/best_model --mode mask --text 'The capital of France is [MASK].'")
        print()
        print("5. 文本相似度计算:")
        print("   python run.py infer --model_path ./bert_output/best_model --mode similarity --text 'I love cats.' --text2 'I adore felines.'")
        print()
        print("6. 获取文本嵌入:")
        print("   python run.py infer --model_path ./bert_output/best_model --mode embedding --text 'This is a test sentence.'")
        print()
        print("注意: 在Mac M1上会自动使用MPS加速")
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
