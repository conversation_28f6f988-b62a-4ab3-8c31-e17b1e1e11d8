"""
BERT数据加载器 - 支持MLM和NSP两个预训练任务
"""

import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
from datasets import load_dataset
import random
import logging
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
from tqdm import tqdm

from config import DataConfig, TrainingConfig

logger = logging.getLogger('BERT')


class BertDataCollator:
    """BERT数据整理器，支持MLM和NSP任务"""
    
    def __init__(self, tokenizer, mlm_probability: float = 0.15, max_length: int = 128):
        self.tokenizer = tokenizer
        self.mlm_probability = mlm_probability
        self.max_length = max_length
    
    def __call__(self, examples: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """
        整理批次数据并应用MLM掩码和NSP标签
        
        Args:
            examples: 批次样本列表
        
        Returns:
            整理后的批次数据
        """
        batch = {}
        
        # 收集所有字段
        input_ids = [example["input_ids"] for example in examples]
        token_type_ids = [example["token_type_ids"] for example in examples]
        attention_mask = [example["attention_mask"] for example in examples]
        next_sentence_labels = [example["next_sentence_label"] for example in examples]
        
        # 填充到相同长度
        batch_encoding = self.tokenizer.pad(
            {"input_ids": input_ids, "token_type_ids": token_type_ids, "attention_mask": attention_mask},
            padding=True,
            max_length=self.max_length,
            return_tensors="pt"
        )
        
        batch["input_ids"] = batch_encoding["input_ids"]
        batch["token_type_ids"] = batch_encoding["token_type_ids"]
        batch["attention_mask"] = batch_encoding["attention_mask"]
        batch["next_sentence_label"] = torch.tensor(next_sentence_labels, dtype=torch.long)
        
        # 应用MLM掩码
        batch["input_ids"], batch["labels"] = self.mask_tokens(batch["input_ids"])
        
        return batch
    
    def mask_tokens(self, inputs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        对输入应用MLM掩码
        
        Args:
            inputs: 输入token ids
        
        Returns:
            masked_inputs: 掩码后的输入
            labels: MLM标签
        """
        labels = inputs.clone()
        
        # 创建概率掩码
        probability_matrix = torch.full(labels.shape, self.mlm_probability)
        
        # 不掩码特殊token
        special_tokens_mask = torch.zeros_like(labels, dtype=torch.bool)
        for special_token_id in [self.tokenizer.cls_token_id, self.tokenizer.sep_token_id, self.tokenizer.pad_token_id]:
            if special_token_id is not None:
                special_tokens_mask |= (labels == special_token_id)
        
        probability_matrix.masked_fill_(special_tokens_mask, value=0.0)
        
        masked_indices = torch.bernoulli(probability_matrix).bool()
        labels[~masked_indices] = -100  # 只计算被掩码token的损失
        
        # 80%的时间用[MASK]替换
        indices_replaced = torch.bernoulli(torch.full(labels.shape, 0.8)).bool() & masked_indices
        inputs[indices_replaced] = self.tokenizer.mask_token_id
        
        # 10%的时间用随机token替换
        indices_random = torch.bernoulli(torch.full(labels.shape, 0.5)).bool() & masked_indices & ~indices_replaced
        random_words = torch.randint(len(self.tokenizer), labels.shape, dtype=torch.long)
        inputs[indices_random] = random_words[indices_random]
        
        # 剩余10%保持不变
        
        return inputs, labels


class BertPretrainingDataset(Dataset):
    """BERT预训练数据集，支持MLM和NSP任务"""
    
    def __init__(
        self, 
        texts: List[str], 
        tokenizer, 
        max_length: int = 128,
        nsp_probability: float = 0.5
    ):
        self.texts = texts
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.nsp_probability = nsp_probability
        
        # 预处理文本，按句子分割
        self.sentences = []
        for text in texts:
            # 简单的句子分割（可以使用更复杂的方法）
            sentences = [s.strip() for s in text.split('.') if s.strip()]
            self.sentences.extend(sentences)
        
        logger.info(f"创建BERT预训练数据集，文档数量: {len(texts)}, 句子数量: {len(self.sentences)}")
    
    def __len__(self) -> int:
        return len(self.sentences)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """获取单个样本"""
        # 获取句子A
        sentence_a = self.sentences[idx]
        
        # 决定是否为NSP任务创建负样本
        if random.random() < self.nsp_probability:
            # 50%概率：选择下一个句子（正样本）
            if idx + 1 < len(self.sentences):
                sentence_b = self.sentences[idx + 1]
                is_next = 1
            else:
                # 如果没有下一个句子，随机选择一个
                sentence_b = random.choice(self.sentences)
                is_next = 0
        else:
            # 50%概率：随机选择一个句子（负样本）
            sentence_b = random.choice(self.sentences)
            is_next = 0
        
        # 编码句子对
        encoding = self.tokenizer(
            sentence_a,
            sentence_b,
            truncation=True,
            max_length=self.max_length,
            padding="max_length",
            return_tensors="pt"
        )
        
        return {
            "input_ids": encoding["input_ids"].squeeze(0),
            "token_type_ids": encoding["token_type_ids"].squeeze(0),
            "attention_mask": encoding["attention_mask"].squeeze(0),
            "next_sentence_label": is_next
        }


class BertClassificationDataset(Dataset):
    """BERT分类数据集"""
    
    def __init__(
        self, 
        texts: List[str], 
        labels: List[int],
        tokenizer, 
        max_length: int = 128
    ):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        logger.info(f"创建BERT分类数据集，样本数量: {len(texts)}")
    
    def __len__(self) -> int:
        return len(self.texts)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """获取单个样本"""
        text = self.texts[idx]
        label = self.labels[idx]
        
        # tokenize文本
        encoding = self.tokenizer(
            text,
            truncation=True,
            max_length=self.max_length,
            padding="max_length",
            return_tensors="pt"
        )
        
        return {
            "input_ids": encoding["input_ids"].squeeze(0),
            "token_type_ids": encoding["token_type_ids"].squeeze(0),
            "attention_mask": encoding["attention_mask"].squeeze(0),
            "labels": torch.tensor(label, dtype=torch.long)
        }


def load_wikitext_for_pretraining(
    dataset_name: str = "Salesforce/wikitext",
    dataset_config: str = "wikitext-103-raw-v1",
    split: str = "train",
    max_samples: Optional[int] = None
) -> List[str]:
    """
    加载WikiText数据集用于预训练
    
    Args:
        dataset_name: 数据集名称
        dataset_config: 数据集配置
        split: 数据集分割
        max_samples: 最大样本数量
    
    Returns:
        文本列表
    """
    logger.info(f"加载预训练数据集: {dataset_name}, 配置: {dataset_config}, split: {split}")
    
    try:
        dataset = load_dataset(dataset_name, dataset_config, split=split)
        
        texts = []
        for i, example in enumerate(tqdm(dataset, desc="加载预训练数据")):
            if max_samples and i >= max_samples:
                break
            
            text = example['text'].strip()
            # 过滤空文本、标题行和过短的文本
            if text and len(text) > 20 and not text.startswith('='):
                texts.append(text)
        
        logger.info(f"成功加载 {len(texts)} 个预训练文本样本")
        return texts
        
    except Exception as e:
        logger.error(f"加载预训练数据集失败: {e}")
        # 返回示例数据
        logger.warning("使用示例数据作为fallback")
        return [
            "The quick brown fox jumps over the lazy dog. This is a sample sentence for training.",
            "Machine learning is a subset of artificial intelligence. It focuses on algorithms that learn from data.",
            "Natural language processing enables computers to understand human language. It has many applications.",
            "Deep learning uses neural networks with multiple layers. These networks can learn complex patterns.",
            "Transformers have revolutionized natural language processing. They use attention mechanisms effectively."
        ] * 20


def load_imdb_dataset(
    dataset_name: str = "noob123/imdb_review_3000",
    split: str = "train",
    max_samples: Optional[int] = None
) -> Tuple[List[str], List[int]]:
    """
    加载IMDB数据集用于分类微调
    
    Args:
        dataset_name: 数据集名称
        split: 数据集分割
        max_samples: 最大样本数量
    
    Returns:
        (文本列表, 标签列表)
    """
    logger.info(f"加载分类数据集: {dataset_name}, split: {split}")
    
    try:
        dataset = load_dataset(dataset_name, split=split)
        
        texts = []
        labels = []
        
        for i, example in enumerate(tqdm(dataset, desc="加载分类数据")):
            if max_samples and i >= max_samples:
                break
            
            texts.append(example['text'])
            # 假设标签是字符串，需要转换为数字
            if isinstance(example['label'], str):
                label = 1 if example['label'].lower() == 'positive' else 0
            else:
                label = example['label']
            labels.append(label)
        
        logger.info(f"成功加载 {len(texts)} 个分类样本")
        return texts, labels
        
    except Exception as e:
        logger.error(f"加载分类数据集失败: {e}")
        # 返回示例数据
        logger.warning("使用示例数据作为fallback")
        texts = [
            "This movie is absolutely fantastic! I loved every minute of it.",
            "Terrible film, waste of time and money.",
            "Great acting and wonderful storyline. Highly recommended.",
            "Boring and predictable. Not worth watching.",
            "Amazing cinematography and excellent direction."
        ] * 10
        labels = [1, 0, 1, 0, 1] * 10
        return texts, labels


def create_pretraining_dataloader(
    data_config: DataConfig,
    training_config: TrainingConfig,
    split: str = "train",
    max_samples: Optional[int] = None
) -> Tuple[DataLoader, Any]:
    """
    创建预训练数据加载器
    
    Args:
        data_config: 数据配置
        training_config: 训练配置
        split: 数据集分割
        max_samples: 最大样本数量
    
    Returns:
        (数据加载器, tokenizer)
    """
    # 加载tokenizer
    logger.info(f"加载tokenizer: {data_config.tokenizer_name}")
    tokenizer = AutoTokenizer.from_pretrained(data_config.tokenizer_name)
    
    # 确保有必要的特殊token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    if tokenizer.mask_token is None:
        tokenizer.add_special_tokens({'mask_token': '[MASK]'})
    
    # 加载数据
    texts = load_wikitext_for_pretraining(
        dataset_name=training_config.dataset_name,
        dataset_config=training_config.dataset_config or "wikitext-103-raw-v1",
        split=split,
        max_samples=max_samples
    )
    
    # 创建数据集
    dataset = BertPretrainingDataset(
        texts=texts,
        tokenizer=tokenizer,
        max_length=data_config.max_length
    )
    
    # 创建数据整理器
    data_collator = BertDataCollator(
        tokenizer=tokenizer,
        mlm_probability=data_config.mlm_probability,
        max_length=data_config.max_length
    )
    
    # 创建数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=training_config.batch_size,
        shuffle=(split == "train"),
        num_workers=training_config.num_workers,
        collate_fn=data_collator,
        pin_memory=True
    )
    
    logger.info(f"创建预训练数据加载器完成，批次数量: {len(dataloader)}")
    
    return dataloader, tokenizer


def create_classification_dataloader(
    texts: List[str],
    labels: List[int],
    tokenizer,
    batch_size: int = 16,
    max_length: int = 128,
    shuffle: bool = True
) -> DataLoader:
    """
    创建分类数据加载器
    
    Args:
        texts: 文本列表
        labels: 标签列表
        tokenizer: tokenizer
        batch_size: 批次大小
        max_length: 最大长度
        shuffle: 是否打乱
    
    Returns:
        数据加载器
    """
    dataset = BertClassificationDataset(
        texts=texts,
        labels=labels,
        tokenizer=tokenizer,
        max_length=max_length
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        pin_memory=True
    )
    
    return dataloader
