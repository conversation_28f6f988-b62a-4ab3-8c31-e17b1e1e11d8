"""
BERT配置文件
使用Pydantic定义所有数据结构和超参数
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
import torch


class BertConfig(BaseModel):
    """BERT模型配置"""

    # 模型架构参数
    vocab_size: int = Field(default=30522, description="词汇表大小")
    hidden_size: int = Field(default=768, description="隐藏层维度")
    num_hidden_layers: int = Field(default=12, description="Transformer层数")
    num_attention_heads: int = Field(default=12, description="注意力头数")
    intermediate_size: int = Field(default=3072, description="前馈网络中间层维度")
    max_position_embeddings: int = Field(default=512, description="最大位置编码长度")
    type_vocab_size: int = Field(default=2, description="句子类型词汇表大小")

    # 正则化参数
    hidden_dropout_prob: float = Field(default=0.1, description="隐藏层dropout概率")
    attention_probs_dropout_prob: float = Field(default=0.1, description="注意力dropout概率")
    layer_norm_eps: float = Field(default=1e-12, description="LayerNorm的epsilon值")

    # 激活函数
    hidden_act: str = Field(default="gelu", description="隐藏层激活函数")

    # 初始化参数
    initializer_range: float = Field(default=0.02, description="权重初始化范围")

    # 任务类型（用于分类任务）
    problem_type: Optional[str] = Field(default=None, description="问题类型")

    class Config:
        """Pydantic配置"""
        extra = "forbid"  # 禁止额外字段


class TrainingConfig(BaseModel):
    """训练配置"""

    # 基础训练参数
    batch_size: int = Field(default=16, description="批次大小")
    learning_rate: float = Field(default=5e-5, description="学习率")
    num_epochs: int = Field(default=1, description="训练轮数")
    warmup_steps: int = Field(default=1000, description="学习率预热步数")
    max_grad_norm: float = Field(default=1.0, description="梯度裁剪阈值")

    # 优化器参数
    weight_decay: float = Field(default=0.01, description="权重衰减")
    adam_epsilon: float = Field(default=1e-8, description="Adam优化器epsilon")
    adam_beta1: float = Field(default=0.9, description="Adam优化器beta1")
    adam_beta2: float = Field(default=0.999, description="Adam优化器beta2")

    # 数据参数
    max_seq_length: int = Field(default=128, description="最大序列长度")
    mlm_probability: float = Field(default=0.15, description="MLM掩码概率")

    # 设备和并行
    device: str = Field(default="auto", description="训练设备")
    num_workers: int = Field(default=4, description="数据加载器工作进程数")

    # 保存和日志
    save_steps: int = Field(default=1000, description="模型保存步数间隔")
    logging_steps: int = Field(default=100, description="日志记录步数间隔")
    output_dir: str = Field(default="./bert_output", description="输出目录")
    save_total_limit: int = Field(default=3, description="保存模型的最大数量")

    # 数据集参数
    dataset_name: str = Field(default="Salesforce/wikitext", description="数据集名称")
    dataset_config: Optional[str] = Field(default="wikitext-103-raw-v1", description="数据集配置")
    train_split: str = Field(default="train", description="训练集分割")
    validation_split: Optional[str] = Field(default="validation", description="验证集分割")

    class Config:
        """Pydantic配置"""
        extra = "forbid"


class DataConfig(BaseModel):
    """数据处理配置"""

    # tokenizer参数
    tokenizer_name: str = Field(default="bert-base-uncased", description="tokenizer名称")
    do_lower_case: bool = Field(default=True, description="是否转换为小写")

    # 数据预处理
    max_length: int = Field(default=128, description="最大序列长度")
    padding: str = Field(default="max_length", description="填充策略")
    truncation: bool = Field(default=True, description="是否截断")

    # MLM参数
    mlm_probability: float = Field(default=0.15, description="掩码概率")
    mask_token: str = Field(default="[MASK]", description="掩码token")

    class Config:
        """Pydantic配置"""
        extra = "forbid"


class LoggingConfig(BaseModel):
    """日志配置"""

    log_level: str = Field(default="INFO", description="日志级别")
    log_file: Optional[str] = Field(default="bert_training.log", description="日志文件路径")
    console_output: bool = Field(default=True, description="是否输出到控制台")

    class Config:
        """Pydantic配置"""
        extra = "forbid"


def get_device() -> torch.device:
    """获取可用的设备"""
    if torch.backends.mps.is_available():
        return torch.device("mps")
    elif torch.cuda.is_available():
        return torch.device("cuda")
    else:
        return torch.device("cpu")


def create_default_config() -> Dict[str, Any]:
    """创建默认配置"""
    return {
        "model": BertConfig(),
        "training": TrainingConfig(),
        "data": DataConfig(),
        "logging": LoggingConfig()
    }
