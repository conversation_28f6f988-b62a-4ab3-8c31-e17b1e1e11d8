"""
BERT模型实现
包含完整的BERT架构：Embedding、Multi-Head Attention、Encoder等
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple, Dict, Any
import logging

from config import BertConfig

logger = logging.getLogger('BERT')


class BertEmbeddings(nn.Module):
    """BERT嵌入层：词嵌入 + 位置嵌入 + 类型嵌入"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.config = config

        # 词嵌入
        self.word_embeddings = nn.Embedding(
            config.vocab_size,
            config.hidden_size,
            padding_idx=0
        )

        # 位置嵌入
        self.position_embeddings = nn.Embedding(
            config.max_position_embeddings,
            config.hidden_size
        )

        # 类型嵌入（用于区分句子A和句子B）
        self.token_type_embeddings = nn.Embedding(
            config.type_vocab_size,
            config.hidden_size
        )

        # LayerNorm和Dropout
        self.LayerNorm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)
        self.dropout = nn.Dropout(config.hidden_dropout_prob)

        # 注册位置id缓冲区
        self.register_buffer(
            "position_ids",
            torch.arange(config.max_position_embeddings).expand((1, -1))
        )

    def forward(
        self,
        input_ids: torch.Tensor,
        token_type_ids: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        前向传播

        Args:
            input_ids: 输入token ids, shape: (batch_size, seq_len)
            token_type_ids: token类型ids, shape: (batch_size, seq_len)
            position_ids: 位置ids, shape: (batch_size, seq_len)

        Returns:
            embeddings: 嵌入向量, shape: (batch_size, seq_len, hidden_size)
        """
        batch_size, seq_len = input_ids.shape

        # 获取位置ids
        if position_ids is None:
            position_ids = self.position_ids[:, :seq_len]

        # 获取token类型ids
        if token_type_ids is None:
            token_type_ids = torch.zeros_like(input_ids)

        # 计算各种嵌入
        word_embeddings = self.word_embeddings(input_ids)
        position_embeddings = self.position_embeddings(position_ids)
        token_type_embeddings = self.token_type_embeddings(token_type_ids)

        # 合并嵌入
        embeddings = word_embeddings + position_embeddings + token_type_embeddings

        # LayerNorm和Dropout
        embeddings = self.LayerNorm(embeddings)
        embeddings = self.dropout(embeddings)

        return embeddings


class BertSelfAttention(nn.Module):
    """BERT自注意力机制"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.config = config

        if config.hidden_size % config.num_attention_heads != 0:
            raise ValueError(
                f"hidden_size ({config.hidden_size}) 必须能被 num_attention_heads "
                f"({config.num_attention_heads}) 整除"
            )

        self.num_attention_heads = config.num_attention_heads
        self.attention_head_size = config.hidden_size // config.num_attention_heads
        self.all_head_size = self.num_attention_heads * self.attention_head_size

        # Query, Key, Value线性变换
        self.query = nn.Linear(config.hidden_size, self.all_head_size)
        self.key = nn.Linear(config.hidden_size, self.all_head_size)
        self.value = nn.Linear(config.hidden_size, self.all_head_size)

        # Dropout
        self.dropout = nn.Dropout(config.attention_probs_dropout_prob)

    def transpose_for_scores(self, x: torch.Tensor) -> torch.Tensor:
        """重塑张量以适应多头注意力计算"""
        new_x_shape = x.size()[:-1] + (self.num_attention_heads, self.attention_head_size)
        x = x.view(new_x_shape)
        return x.permute(0, 2, 1, 3)  # (batch_size, num_heads, seq_len, head_size)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播

        Args:
            hidden_states: 输入隐藏状态, shape: (batch_size, seq_len, hidden_size)
            attention_mask: 注意力掩码, shape: (batch_size, seq_len, seq_len)

        Returns:
            context_layer: 上下文向量, shape: (batch_size, seq_len, hidden_size)
            attention_probs: 注意力权重, shape: (batch_size, num_heads, seq_len, seq_len)
        """
        # 计算Query, Key, Value
        query_layer = self.transpose_for_scores(self.query(hidden_states))
        key_layer = self.transpose_for_scores(self.key(hidden_states))
        value_layer = self.transpose_for_scores(self.value(hidden_states))

        # 计算注意力分数
        attention_scores = torch.matmul(query_layer, key_layer.transpose(-1, -2))
        attention_scores = attention_scores / math.sqrt(self.attention_head_size)

        # 应用注意力掩码
        if attention_mask is not None:
            attention_scores = attention_scores + attention_mask

        # 计算注意力概率
        attention_probs = F.softmax(attention_scores, dim=-1)
        attention_probs = self.dropout(attention_probs)

        # 计算上下文向量
        context_layer = torch.matmul(attention_probs, value_layer)

        # 重塑输出
        context_layer = context_layer.permute(0, 2, 1, 3).contiguous()
        new_context_layer_shape = context_layer.size()[:-2] + (self.all_head_size,)
        context_layer = context_layer.view(new_context_layer_shape)

        return context_layer, attention_probs


class BertSelfOutput(nn.Module):
    """BERT自注意力输出层"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.dense = nn.Linear(config.hidden_size, config.hidden_size)
        self.LayerNorm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)
        self.dropout = nn.Dropout(config.hidden_dropout_prob)

    def forward(self, hidden_states: torch.Tensor, input_tensor: torch.Tensor) -> torch.Tensor:
        """
        前向传播（包含残差连接）

        Args:
            hidden_states: 自注意力输出
            input_tensor: 残差连接的输入

        Returns:
            输出张量
        """
        hidden_states = self.dense(hidden_states)
        hidden_states = self.dropout(hidden_states)
        hidden_states = self.LayerNorm(hidden_states + input_tensor)
        return hidden_states


class BertAttention(nn.Module):
    """BERT注意力模块（自注意力 + 输出层）"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.self = BertSelfAttention(config)
        self.output = BertSelfOutput(config)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """前向传播"""
        self_outputs = self.self(hidden_states, attention_mask)
        attention_output = self.output(self_outputs[0], hidden_states)
        return attention_output, self_outputs[1]  # 返回输出和注意力权重


class BertIntermediate(nn.Module):
    """BERT中间层（前馈网络的第一部分）"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.dense = nn.Linear(config.hidden_size, config.intermediate_size)

        # 激活函数
        if config.hidden_act == "gelu":
            self.intermediate_act_fn = F.gelu
        elif config.hidden_act == "relu":
            self.intermediate_act_fn = F.relu
        else:
            raise ValueError(f"不支持的激活函数: {config.hidden_act}")

    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        hidden_states = self.dense(hidden_states)
        hidden_states = self.intermediate_act_fn(hidden_states)
        return hidden_states


class BertOutput(nn.Module):
    """BERT输出层（前馈网络的第二部分）"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.dense = nn.Linear(config.intermediate_size, config.hidden_size)
        self.LayerNorm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)
        self.dropout = nn.Dropout(config.hidden_dropout_prob)

    def forward(self, hidden_states: torch.Tensor, input_tensor: torch.Tensor) -> torch.Tensor:
        """前向传播（包含残差连接）"""
        hidden_states = self.dense(hidden_states)
        hidden_states = self.dropout(hidden_states)
        hidden_states = self.LayerNorm(hidden_states + input_tensor)
        return hidden_states


class BertLayer(nn.Module):
    """BERT单层Transformer"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.attention = BertAttention(config)
        self.intermediate = BertIntermediate(config)
        self.output = BertOutput(config)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播

        Args:
            hidden_states: 输入隐藏状态
            attention_mask: 注意力掩码

        Returns:
            layer_output: 层输出
            attention_probs: 注意力权重
        """
        # 自注意力
        attention_output, attention_probs = self.attention(hidden_states, attention_mask)

        # 前馈网络
        intermediate_output = self.intermediate(attention_output)
        layer_output = self.output(intermediate_output, attention_output)

        return layer_output, attention_probs


class BertEncoder(nn.Module):
    """BERT编码器（多层Transformer堆叠）"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.config = config
        self.layer = nn.ModuleList([BertLayer(config) for _ in range(config.num_hidden_layers)])

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        output_attentions: bool = False,
        output_hidden_states: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            hidden_states: 输入隐藏状态
            attention_mask: 注意力掩码
            output_attentions: 是否输出注意力权重
            output_hidden_states: 是否输出所有隐藏状态

        Returns:
            包含输出的字典
        """
        all_hidden_states = () if output_hidden_states else None
        all_attentions = () if output_attentions else None

        for layer_module in self.layer:
            if output_hidden_states:
                all_hidden_states = all_hidden_states + (hidden_states,)

            layer_outputs = layer_module(hidden_states, attention_mask)
            hidden_states = layer_outputs[0]

            if output_attentions:
                all_attentions = all_attentions + (layer_outputs[1],)

        if output_hidden_states:
            all_hidden_states = all_hidden_states + (hidden_states,)

        return {
            "last_hidden_state": hidden_states,
            "hidden_states": all_hidden_states,
            "attentions": all_attentions
        }


class BertPooler(nn.Module):
    """BERT池化层（用于分类任务）"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.dense = nn.Linear(config.hidden_size, config.hidden_size)
        self.activation = nn.Tanh()

    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            hidden_states: 编码器输出, shape: (batch_size, seq_len, hidden_size)

        Returns:
            pooled_output: 池化输出, shape: (batch_size, hidden_size)
        """
        # 取第一个token（[CLS]）的隐藏状态
        first_token_tensor = hidden_states[:, 0]
        pooled_output = self.dense(first_token_tensor)
        pooled_output = self.activation(pooled_output)
        return pooled_output


class BertModel(nn.Module):
    """完整的BERT模型"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.config = config

        # 各个组件
        self.embeddings = BertEmbeddings(config)
        self.encoder = BertEncoder(config)
        self.pooler = BertPooler(config)

        # 初始化权重
        self.apply(self._init_weights)

        logger.info(f"BERT模型初始化完成，参数数量: {self.count_parameters():,}")

    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.Embedding):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.padding_idx is not None:
                module.weight.data[module.padding_idx].zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def count_parameters(self) -> int:
        """计算参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

    def get_input_embeddings(self):
        """获取输入嵌入层"""
        return self.embeddings.word_embeddings

    def set_input_embeddings(self, value):
        """设置输入嵌入层"""
        self.embeddings.word_embeddings = value

    def _create_attention_mask(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """创建注意力掩码"""
        if attention_mask is None:
            attention_mask = (input_ids != 0).float()

        # 扩展维度以适应多头注意力
        batch_size, seq_len = attention_mask.shape
        attention_mask = attention_mask.view(batch_size, 1, 1, seq_len)

        # 将掩码转换为注意力分数的加法形式
        attention_mask = (1.0 - attention_mask) * -10000.0

        return attention_mask

    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        token_type_ids: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        output_attentions: bool = False,
        output_hidden_states: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            input_ids: 输入token ids
            attention_mask: 注意力掩码
            token_type_ids: token类型ids
            position_ids: 位置ids
            output_attentions: 是否输出注意力权重
            output_hidden_states: 是否输出所有隐藏状态

        Returns:
            包含模型输出的字典
        """
        # 创建注意力掩码
        attention_mask = self._create_attention_mask(input_ids, attention_mask)

        # 嵌入层
        embedding_output = self.embeddings(
            input_ids=input_ids,
            token_type_ids=token_type_ids,
            position_ids=position_ids
        )

        # 编码器
        encoder_outputs = self.encoder(
            hidden_states=embedding_output,
            attention_mask=attention_mask,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states
        )

        # 池化层
        pooled_output = self.pooler(encoder_outputs["last_hidden_state"])

        return {
            "last_hidden_state": encoder_outputs["last_hidden_state"],
            "pooler_output": pooled_output,
            "hidden_states": encoder_outputs["hidden_states"],
            "attentions": encoder_outputs["attentions"]
        }


class BertLMPredictionHead(nn.Module):
    """BERT语言模型预测头（用于MLM任务）"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.transform = nn.Linear(config.hidden_size, config.hidden_size)
        self.activation = F.gelu
        self.LayerNorm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)

        # 输出权重与输入嵌入权重共享
        self.decoder = nn.Linear(config.hidden_size, config.vocab_size, bias=False)
        self.bias = nn.Parameter(torch.zeros(config.vocab_size))
        self.decoder.bias = self.bias

    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            hidden_states: 编码器输出, shape: (batch_size, seq_len, hidden_size)

        Returns:
            prediction_scores: 预测分数, shape: (batch_size, seq_len, vocab_size)
        """
        hidden_states = self.transform(hidden_states)
        hidden_states = self.activation(hidden_states)
        hidden_states = self.LayerNorm(hidden_states)
        hidden_states = self.decoder(hidden_states)
        return hidden_states


class BertForMaskedLM(nn.Module):
    """用于掩码语言模型的BERT"""

    def __init__(self, config: BertConfig):
        super().__init__()
        self.config = config

        self.bert = BertModel(config)
        self.cls = BertLMPredictionHead(config)

        # 权重共享
        self.cls.decoder.weight = self.bert.embeddings.word_embeddings.weight

        # 初始化权重
        self.apply(self._init_weights)

        logger.info(f"BertForMaskedLM初始化完成，参数数量: {self.count_parameters():,}")

    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.Embedding):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.padding_idx is not None:
                module.weight.data[module.padding_idx].zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def count_parameters(self) -> int:
        """计算参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        token_type_ids: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None,
        output_attentions: bool = False,
        output_hidden_states: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            input_ids: 输入token ids
            attention_mask: 注意力掩码
            token_type_ids: token类型ids
            position_ids: 位置ids
            labels: MLM标签
            output_attentions: 是否输出注意力权重
            output_hidden_states: 是否输出所有隐藏状态

        Returns:
            包含损失和预测的字典
        """
        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states
        )

        sequence_output = outputs["last_hidden_state"]
        prediction_scores = self.cls(sequence_output)

        masked_lm_loss = None
        if labels is not None:
            loss_fct = nn.CrossEntropyLoss()  # -100 index = padding token
            masked_lm_loss = loss_fct(prediction_scores.view(-1, self.config.vocab_size), labels.view(-1))

        return {
            "loss": masked_lm_loss,
            "logits": prediction_scores,
            "hidden_states": outputs["hidden_states"],
            "attentions": outputs["attentions"]
        }

    def get_input_embeddings(self):
        """获取输入嵌入层"""
        return self.bert.embeddings.word_embeddings

    def set_input_embeddings(self, value):
        """设置输入嵌入层"""
        self.bert.embeddings.word_embeddings = value
        self.cls.decoder.weight = value
