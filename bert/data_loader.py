"""
数据加载和预处理模块
支持HuggingFace数据集和MLM任务的数据处理
"""

import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
from datasets import load_dataset
import random
import logging
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
from tqdm import tqdm

from config import DataConfig, TrainingConfig

logger = logging.getLogger('BERT')


class MLMDataCollator:
    """MLM数据整理器，负责动态掩码"""

    def __init__(self, tokenizer, mlm_probability: float = 0.15):
        self.tokenizer = tokenizer
        self.mlm_probability = mlm_probability

    def __call__(self, examples: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
        """
        整理批次数据并应用MLM掩码

        Args:
            examples: 批次样本列表

        Returns:
            整理后的批次数据
        """
        batch = self.tokenizer.pad(examples, return_tensors="pt")

        # 应用MLM掩码
        batch["input_ids"], batch["labels"] = self.mask_tokens(
            batch["input_ids"],
            special_tokens_mask=batch.get("special_tokens_mask")
        )

        # 移除special_tokens_mask，因为模型不需要这个参数
        if "special_tokens_mask" in batch:
            del batch["special_tokens_mask"]

        return batch

    def mask_tokens(
        self,
        inputs: torch.Tensor,
        special_tokens_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        对输入应用MLM掩码

        Args:
            inputs: 输入token ids
            special_tokens_mask: 特殊token掩码

        Returns:
            masked_inputs: 掩码后的输入
            labels: MLM标签
        """
        labels = inputs.clone()

        # 创建概率掩码
        probability_matrix = torch.full(labels.shape, self.mlm_probability)

        if special_tokens_mask is not None:
            probability_matrix.masked_fill_(special_tokens_mask.bool(), value=0.0)

        # 不掩码padding tokens
        if self.tokenizer.pad_token_id is not None:
            padding_mask = labels.eq(self.tokenizer.pad_token_id)
            probability_matrix.masked_fill_(padding_mask, value=0.0)

        masked_indices = torch.bernoulli(probability_matrix).bool()
        labels[~masked_indices] = -100  # 只计算被掩码token的损失

        # 80%的时间用[MASK]替换
        indices_replaced = torch.bernoulli(torch.full(labels.shape, 0.8)).bool() & masked_indices
        inputs[indices_replaced] = self.tokenizer.mask_token_id

        # 10%的时间用随机token替换
        indices_random = torch.bernoulli(torch.full(labels.shape, 0.5)).bool() & masked_indices & ~indices_replaced
        random_words = torch.randint(len(self.tokenizer), labels.shape, dtype=torch.long)
        inputs[indices_random] = random_words[indices_random]

        # 剩余10%保持不变

        return inputs, labels


class TextDataset(Dataset):
    """文本数据集类"""

    def __init__(
        self,
        texts: List[str],
        tokenizer,
        max_length: int = 128,
        return_special_tokens_mask: bool = True
    ):
        self.texts = texts
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.return_special_tokens_mask = return_special_tokens_mask

        logger.info(f"创建文本数据集，样本数量: {len(texts)}")

    def __len__(self) -> int:
        return len(self.texts)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """获取单个样本"""
        text = self.texts[idx]

        # tokenize文本
        encoding = self.tokenizer(
            text,
            truncation=True,
            max_length=self.max_length,
            padding=False,  # 在collator中进行padding
            return_tensors="pt",
            return_special_tokens_mask=self.return_special_tokens_mask
        )

        # 移除batch维度
        item = {key: val.squeeze(0) for key, val in encoding.items()}

        return item


def load_wikitext_dataset(
    dataset_name: str = "Salesforce/wikitext",
    dataset_config: str = "wikitext-103-raw-v1",
    split: str = "train",
    max_samples: Optional[int] = None,
    streaming: bool = False
) -> List[str]:
    """
    加载WikiText数据集

    Args:
        dataset_name: 数据集名称
        dataset_config: 数据集配置（wikitext-103-raw-v1, wikitext-2-raw-v1等）
        split: 数据集分割
        max_samples: 最大样本数量
        streaming: 是否使用流式加载

    Returns:
        文本列表
    """
    logger.info(f"加载数据集: {dataset_name}, 配置: {dataset_config}, split: {split}")

    try:
        # 加载数据集
        dataset = load_dataset(dataset_name, dataset_config, split=split, streaming=streaming)

        texts = []
        if streaming:
            # 流式处理
            for i, example in enumerate(tqdm(dataset, desc="加载数据")):
                if max_samples and i >= max_samples:
                    break
                # 过滤空文本和只有标题的文本
                text = example['text'].strip()
                if text and len(text) > 10 and not text.startswith('='):
                    texts.append(text)
        else:
            # 一次性加载
            all_texts = dataset['text']

            # 过滤和预处理文本
            for text in tqdm(all_texts, desc="预处理文本"):
                text = text.strip()
                # 过滤空文本、标题行和过短的文本
                if text and len(text) > 10 and not text.startswith('='):
                    texts.append(text)

                if max_samples and len(texts) >= max_samples:
                    break

        logger.info(f"成功加载 {len(texts)} 个文本样本")
        return texts

    except Exception as e:
        logger.error(f"加载数据集失败: {e}")
        logger.info("尝试加载备用配置...")

        # 尝试其他配置
        backup_configs = ["wikitext-2-raw-v1", "wikitext-103-v1", "wikitext-2-v1"]
        for backup_config in backup_configs:
            if backup_config != dataset_config:
                try:
                    logger.info(f"尝试配置: {backup_config}")
                    dataset = load_dataset(dataset_name, backup_config, split=split)

                    texts = []
                    all_texts = dataset['text']

                    for text in tqdm(all_texts[:max_samples] if max_samples else all_texts, desc="预处理文本"):
                        text = text.strip()
                        if text and len(text) > 10 and not text.startswith('='):
                            texts.append(text)

                    logger.info(f"使用备用配置 {backup_config} 成功加载 {len(texts)} 个文本样本")
                    return texts

                except Exception as backup_e:
                    logger.warning(f"备用配置 {backup_config} 也失败: {backup_e}")
                    continue

        # 如果所有配置都失败，返回示例数据
        logger.warning("所有配置都失败，使用示例数据作为fallback")
        return [
            "The quick brown fox jumps over the lazy dog. This is a sample sentence for training.",
            "Machine learning is a subset of artificial intelligence that focuses on algorithms.",
            "Natural language processing enables computers to understand and generate human language.",
            "Deep learning uses neural networks with multiple layers to learn complex patterns.",
            "Transformers have revolutionized the field of natural language processing.",
            "BERT is a bidirectional encoder representation from transformers model.",
            "Attention mechanisms allow models to focus on relevant parts of the input.",
            "Pre-training on large corpora helps models learn general language understanding.",
            "Fine-tuning adapts pre-trained models to specific downstream tasks.",
            "Language models can generate coherent and contextually relevant text."
        ] * 50  # 重复以提供足够的训练数据


def create_data_loader(
    data_config: DataConfig,
    training_config: TrainingConfig,
    split: str = "train",
    max_samples: Optional[int] = None
) -> DataLoader:
    """
    创建数据加载器

    Args:
        data_config: 数据配置
        training_config: 训练配置
        split: 数据集分割
        max_samples: 最大样本数量

    Returns:
        数据加载器
    """
    # 加载tokenizer
    logger.info(f"加载tokenizer: {data_config.tokenizer_name}")
    tokenizer = AutoTokenizer.from_pretrained(
        data_config.tokenizer_name,
        do_lower_case=data_config.do_lower_case
    )

    # 确保有必要的特殊token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    if tokenizer.mask_token is None:
        tokenizer.add_special_tokens({'mask_token': '[MASK]'})

    # 加载数据
    texts = load_wikitext_dataset(
        dataset_name=training_config.dataset_name,
        dataset_config=training_config.dataset_config or "wikitext-103-raw-v1",
        split=split,
        max_samples=max_samples
    )

    # 创建数据集
    dataset = TextDataset(
        texts=texts,
        tokenizer=tokenizer,
        max_length=data_config.max_length,
        return_special_tokens_mask=True
    )

    # 创建数据整理器
    data_collator = MLMDataCollator(
        tokenizer=tokenizer,
        mlm_probability=data_config.mlm_probability
    )

    # 创建数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=training_config.batch_size,
        shuffle=(split == "train"),
        num_workers=training_config.num_workers,
        collate_fn=data_collator,
        pin_memory=True
    )

    logger.info(f"创建数据加载器完成，批次数量: {len(dataloader)}")

    return dataloader, tokenizer


def get_vocab_size_from_tokenizer(tokenizer_name: str) -> int:
    """获取tokenizer的词汇表大小"""
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
    return len(tokenizer)
