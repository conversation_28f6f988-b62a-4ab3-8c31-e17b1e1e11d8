我准备使用Python的torch库从0实现bert框架，请你给我实现
    要求： 
    1:不用参考现有的项目文件，按你的理解从0实现,且可以新建文件,在deepai/bert目录下实现
    1：我的电脑是mac M1,使用gpu训练
    2：可使用使用hugginface 上的roneneldan/TinyStories-33M数据集下做预训练
    4：可以使用hugginface下载数据集，且可以使用huggingface的transformer库。
    7：实现完整的bert框架，我需要使用这份代码串联学习
    8：保存训练好的模型
    9: 可以使用pytorch中提供的方法，如nn.Embedding,nn.LayerNorm等,但是需要有bert框架的完整实现，供我系统学习
    10: 代码结构清晰，日志完善，注释完善
    11: 代码中涉及数据结构或超参数的，最好使用pydantic库用来指明数据结构,
    13: 写好训练和测试代码的入口函数，让我可以一键运行

